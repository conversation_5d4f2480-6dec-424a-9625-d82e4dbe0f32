/**
 * Copyright © 2021 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.batch.writer;

import java.math.BigDecimal;
import java.util.List;

import com.google.common.annotations.VisibleForTesting;
import com.google.inject.Inject;

import org.easybatch.core.record.Batch;
import org.easybatch.core.record.Record;
import org.easybatch.core.writer.RecordWriter;

import jp.ne.interspace.taekkyeon.model.PublisherPaymentPayload;
import jp.ne.interspace.taekkyeon.model.PublisherRewardDetails;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.CampaignClosureMapper;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.PublisherAccountCampaignRewardHistoryMapper;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.PublisherAccountPaymentHistoryMapper;
import jp.ne.interspace.taekkyeon.service.PublisherRewardService;
import jp.ne.interspace.taekkyeon.validator.DatabaseOperationValidator;

import static jp.ne.interspace.taekkyeon.model.PaymentGenerationStatus.IS_PROCESSING;

/**
 * {@link RecordWriter} implementation to upsert publisher payment information.
 *
 * <AUTHOR>
 */
public class PublisherPaymentRecordWriter implements RecordWriter {

    @Inject
    private PublisherAccountCampaignRewardHistoryMapper campaignRewardHistoryMapper;

    @Inject
    private PublisherAccountPaymentHistoryMapper paymentHistoryMapper;

    @Inject
    private DatabaseOperationValidator databaseOperationValidator;

    @Inject
    private PublisherRewardService publisherRewardService;

    @Inject
    private CampaignClosureMapper campaignClosureMapper;

    @Override
    public void open() throws Exception {
        // do nothing
    }

    @Override
    public void writeRecords(Batch batch) throws Exception {
        updateHistoryForPublishersIn(batch);
    }

    @Override
    public void close() throws Exception {
        // do nothing
    }

    @SuppressWarnings("unchecked")
    private void updateHistoryForPublishersIn(Batch batch) {
        for (Record<PublisherPaymentPayload> record : batch) {
            String targetMonth = record.getPayload().getTargetMonth();
            List<PublisherRewardDetails> campaignRewards = record.getPayload()
                    .getCampaignRewards();
            for (PublisherRewardDetails singleCampaignRewards : campaignRewards) {
                Long publisherId = singleCampaignRewards.getPublisherId();
                Long campaignClosureId = singleCampaignRewards.getCampaignClosureId();
                updateCampaignRewardsBy(publisherId, campaignClosureId, targetMonth,
                        singleCampaignRewards);
                updatePaymentHistoryBy(publisherId, campaignClosureId, targetMonth,
                        singleCampaignRewards);
            }
            updateIsPaymentGenerated(record.getPayload().getCampaignClosureId());
        }
    }

    @VisibleForTesting
    void updateIsPaymentGenerated(long id) {
        if (id != 0) {
            campaignClosureMapper.updateIsPaymentGenerated(id, IS_PROCESSING);
        }
    }

    private void updateCampaignRewardsBy(Long publisherId, Long campaignClosureId,
            String targetMonth, PublisherRewardDetails rewardDetails) {
        campaignRewardHistoryMapper.deleteBy(publisherId, campaignClosureId);
        String merchantCurrency = rewardDetails.getMerchantCountryCurrency();
        String publisherCurrency = rewardDetails.getPublisherCountryCurrency();
        BigDecimal publisherReward = publisherRewardService.convertByCurrencyCode(
                rewardDetails.getReward(), merchantCurrency, publisherCurrency,
                targetMonth);
        int insertedCampaignRewardHistoryItemCount = campaignRewardHistoryMapper
                .insertHistoryItemBy(campaignClosureId, targetMonth, publisherId,
                        rewardDetails.getCampaignId(), publisherReward,
                        rewardDetails.getRewardInUsd(), rewardDetails.getClickCount(),
                        rewardDetails.getImpressionCount(), rewardDetails.getSalesCount(),
                        rewardDetails.getConversionBonus(),
                        rewardDetails.getConversionBonusInUsd(),
                        rewardDetails.getFixedBonus(),
                        rewardDetails.getFixedBonusInUsd());
        databaseOperationValidator
                .validateInsertedRowCount(insertedCampaignRewardHistoryItemCount);
    }

    private void updatePaymentHistoryBy(Long publisherId, Long campaignClosureId,
            String targetMonth, PublisherRewardDetails rewardDetails) {
        paymentHistoryMapper.deleteBy(publisherId, campaignClosureId);
        int insertedPaymentHistoryItemCount = paymentHistoryMapper
                .insertHistoryItemBy(publisherId, campaignClosureId, targetMonth);
        databaseOperationValidator
                .validateInsertedRowCount(insertedPaymentHistoryItemCount);
    }
}
