/**
 * Copyright © 2021 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.module;

import javax.inject.Singleton;

import com.google.inject.AbstractModule;
import com.google.inject.Provides;
import com.google.inject.TypeLiteral;
import com.google.inject.name.Named;

import org.easybatch.core.processor.RecordProcessor;
import org.easybatch.core.reader.RecordReader;
import org.easybatch.core.record.Record;
import org.easybatch.core.writer.RecordWriter;

import jp.ne.interspace.taekkyeon.batch.processor.DummyRecordProcessor;
import jp.ne.interspace.taekkyeon.batch.reader.PublisherPaymentRecordReader;
import jp.ne.interspace.taekkyeon.batch.writer.PublisherPaymentRecordWriter;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonPropertiesJunitModule;

import static java.lang.System.getProperty;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.EMPTY;

/**
 * Guice module for the publisher payment calculator batch.
 *
 * <AUTHOR>
 */
public class PublisherPaymentJunitModule extends AbstractModule {

    public static final String BIND_KEY_PAYMENT_COUNTRY_CODE = "payment.country.code";
    private static final String PAYMENT_COUNTRY_CODE = "paymentCountryCode";

    @Override
    protected void configure() {
        install(new TaekkyeonPropertiesJunitModule());

        bind(RecordReader.class).annotatedWith(MainRecordReaderBinding.class)
                .to(PublisherPaymentRecordReader.class);
        bind(new TypeLiteral<RecordProcessor<? extends Record<?>, ? extends Record<?>>>() {
        }).annotatedWith(MainRecordProcessorBinding.class)
                .to(DummyRecordProcessor.class);
        bind(RecordWriter.class).annotatedWith(MainRecordWriterBinding.class)
                .to(PublisherPaymentRecordWriter.class);
    }

    @Provides @Singleton @Named(BIND_KEY_PAYMENT_COUNTRY_CODE)
    private String providePaymentCountryCode() {
        return getProperty(PAYMENT_COUNTRY_CODE, EMPTY);
    }
}
