/**
 * Copyright © 2021 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper;

import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;

import jp.ne.interspace.taekkyeon.multiline.Multiline;

/**
 * MyBatis mapper for the {@code PUBLISHER_ACCOUNT_PAYMENT_HISTORY} DB table.
 *
 * <AUTHOR>
 */
public interface PublisherAccountPaymentHistoryMapper {

    /**
        INSERT INTO
            publisher_account_payment_history (
                id,
                campaign_closure_id,
                publisher_id,
                reward_month,
                payment_state,
                created_by,
                created_on
        ) VALUES (
            publisher_account_payment_history_sequence.NEXTVAL,
            #{campaignClosureId},
            #{publisherId},
            TO_DATE(#{targetMonth}, 'YYYYMM'),
            0,
            'PublisherCampaignClosurePaymentCalculatorBatch',
            SYSDATE
        )
     */
    @Multiline String INSERT_HISTORY_ITEM = "";

    /**
     * Inserts a new payment history entry for the given publisher in the given month.
     *
     * @param publisherId
     *            ID of the given publisher
     * @param campaignClosureId
     *            ID of the given campaign closure id
     * @param targetMonth
     *            the given month
     * @return the number of inserted DB table rows
     */
    @Insert(INSERT_HISTORY_ITEM)
    int insertHistoryItemBy(@Param("publisherId") Long publisherId,
            @Param("campaignClosureId") Long campaignClosureId,
            @Param("targetMonth") String targetMonth);

    /**
        DELETE FROM
            publisher_account_payment_history
        WHERE
            campaign_closure_id = #{campaignClosureId}
        AND
            publisher_id = #{publisherId}
     */
    @Multiline String DELETE_BY_CAMPAIGN_CLOSURE_ID_AND_PUBLISHER_ID = "";

    /**
     * Deletes all payment history items for the given campaign closure id.
     *
     * @param publisherId
     *            the given of publisher id
     * @param campaignClosureId
     *            the given of campaign closure id
     * @return the number of deleted rows
     */
    @Delete(DELETE_BY_CAMPAIGN_CLOSURE_ID_AND_PUBLISHER_ID)
    int deleteBy(@Param("publisherId") Long publisherId,
            @Param("campaignClosureId") Long campaignClosureId);
}
