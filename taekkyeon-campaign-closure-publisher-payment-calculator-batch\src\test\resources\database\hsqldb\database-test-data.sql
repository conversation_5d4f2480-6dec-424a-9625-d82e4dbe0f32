SET DATABASE SQL SYNTAX ORA TRUE;

INSERT INTO CAMPAIGN_CLOSURE(ID, CAMPAIGN_ID, STATUS, CLOSED_FROM, CLOSED_TO, IS_PAYMENT_GENERATED)
VALUES(1, 10, 0, TO_DATE('20211001', 'YYYYMMDD'), TO_DATE('20211015', 'YYYYMMDD'), 0);
INSERT INTO CAMPAIGN_CLOSURE(ID, CAMPAIGN_ID, STATUS, CLOSED_FROM, CLOSED_TO, IS_PAYMENT_GENERATED)
VALUES(2, 10, 2, TO_DATE('********', 'YYYYMMDD'), TO_DATE('********', 'YYYYMMDD'), 0);
INSERT INTO CAMPAIGN_CLOSURE(ID, CAMPAIGN_ID, STATUS, CLOSED_FROM, CLOSED_TO, IS_PAYMENT_GENERATED)
VALUES(3, 1, 2, TO_DATE('********', 'YYYYMMDD'), TO_DATE('********', 'YYYYMMDD'), 0);
INSERT INTO CAMPAIGN_CLOSURE(ID, CAMPAIGN_ID, STATUS, CLOSED_FROM, CLOSED_TO, IS_PAYMENT_GENERATED)
VALUES(4, 1, 2, TO_DATE('********', 'YYYYMMDD'), TO_DATE('********', 'YYYYMMDD'), 0);

INSERT INTO PARTNER_ACCOUNT (ACCOUNT_NO, ACCOUNT_STATE, LOGIN_NAME, APPLIED_DATE, COUNTRY_CODE)
VALUES (1, 1, 'activePublisher', SYSDATE, 'ID');
INSERT INTO PARTNER_ACCOUNT (ACCOUNT_NO, ACCOUNT_STATE, LOGIN_NAME, APPLIED_DATE, COUNTRY_CODE)
VALUES (2, 0, 'inactivePublisher', SYSDATE, 'ID');
INSERT INTO PARTNER_ACCOUNT (ACCOUNT_NO, ACCOUNT_STATE, LOGIN_NAME, APPLIED_DATE, COUNTRY_CODE)
VALUES (3, 2, 'deletedPublisher', SYSDATE, 'ID');

INSERT INTO PARTNER_SITE VALUES (10,1,'siteForRewardData','','',2,0,0,0,0,0,0,0,NULL,NULL,NULL,NULL);
INSERT INTO PARTNER_SITE VALUES (20,2,'siteForInactivePublisher','','',2,0,0,0,0,0,0,0,NULL,NULL,NULL,NULL);
INSERT INTO PARTNER_SITE VALUES (30,3,'siteForDeletedPublisher','','',2,0,0,0,0,0,0,0,NULL,NULL,NULL,NULL);
INSERT INTO PARTNER_SITE (SITE_NO, ACCOUNT_NO) VALUES (1, 1);
INSERT INTO PARTNER_SITE (SITE_NO, ACCOUNT_NO) VALUES (2, 2);
INSERT INTO PARTNER_SITE (SITE_NO, ACCOUNT_NO) VALUES (3, 3);
INSERT INTO PARTNER_SITE (SITE_NO, ACCOUNT_NO) VALUES (4, 3);

INSERT INTO SALES_LOG (SEQ_NO, CREATED_BY, SALES_DATE, CONFIRMED_DATE, MERCHANT_CAMPAIGN_NO, RESULT_ID, SALES_LOG_STATUS, INTERNAL_TRANSACTION_ID, PARTNER_SITE_NO, BANNER_ID, DEVICE_TYPE, SALES_REWARD, PUBLISHER_REWARD_IN_USD, TOTAL_PRICE_REWARD, TOTAL_PRICE, LOG_DATE, TRANSACTION_ID, SALES_COUNT, PRICE, REWARD_TYPE, COMMISSION_TYPE, AT_COMMISSION, AGENT_COMMISSION, DEFAULT_SALES_COUNT, DEFAULT_PRICE, UUID, PUBLISHER_BONUS, PUBLISHER_BONUS_IN_USD)
VALUES (1, 'conversionForRewardData', TO_DATE('2017/11/22', 'YYYY/MM/DD'), TO_DATE('2017/11/22', 'YYYY/MM/DD'), 3, 2, 1, 'internalTransactionId', 10, 200, 1, 100, 900, 200, 300, SYSDATE, '', 3, 0, 0, 0, 0, 0, 0, 0, '', 200, 1.2);
INSERT INTO SALES_LOG (SEQ_NO, CREATED_BY, SALES_DATE, CONFIRMED_DATE, MERCHANT_CAMPAIGN_NO, RESULT_ID, SALES_LOG_STATUS, INTERNAL_TRANSACTION_ID, PARTNER_SITE_NO, BANNER_ID, DEVICE_TYPE, SALES_REWARD, PUBLISHER_REWARD_IN_USD, TOTAL_PRICE_REWARD, TOTAL_PRICE, LOG_DATE, TRANSACTION_ID, SALES_COUNT, PRICE, REWARD_TYPE, COMMISSION_TYPE, AT_COMMISSION, AGENT_COMMISSION, DEFAULT_SALES_COUNT, DEFAULT_PRICE, UUID, PUBLISHER_BONUS, PUBLISHER_BONUS_IN_USD)
VALUES (2, 'conversionForInactivePublisher', TO_DATE('2017/11/22', 'YYYY/MM/DD'), TO_DATE('2017/11/22', 'YYYY/MM/DD'), 3, 2, 1, 'internalTransactionId2', 20, 200, 1, 100, 800, 200, 300, SYSDATE, '', 3, 0, 0, 0, 0, 0, 0, 0, '', 260, 1.4);
INSERT INTO SALES_LOG (SEQ_NO, CREATED_BY, SALES_DATE, CONFIRMED_DATE, MERCHANT_CAMPAIGN_NO, RESULT_ID, SALES_LOG_STATUS, INTERNAL_TRANSACTION_ID, PARTNER_SITE_NO, BANNER_ID, DEVICE_TYPE, SALES_REWARD, PUBLISHER_REWARD_IN_USD, TOTAL_PRICE_REWARD, TOTAL_PRICE, LOG_DATE, TRANSACTION_ID, SALES_COUNT, PRICE, REWARD_TYPE, COMMISSION_TYPE, AT_COMMISSION, AGENT_COMMISSION, DEFAULT_SALES_COUNT, DEFAULT_PRICE, UUID, PUBLISHER_BONUS, PUBLISHER_BONUS_IN_USD)
VALUES (3, 'conversionForDeletedPublisher', TO_DATE('2017/11/22', 'YYYY/MM/DD'), TO_DATE('2017/11/22', 'YYYY/MM/DD'), 3, 2, 1, 'internalTransactionId3', 30, 200, 1, 100, 700, 200, 300, SYSDATE, '', 3, 0, 0, 0, 0, 0, 0, 0, '', 400, 2);
INSERT INTO SALES_LOG (SEQ_NO, CREATED_BY, SALES_DATE, CONFIRMED_DATE, MERCHANT_CAMPAIGN_NO, RESULT_ID, SALES_LOG_STATUS, INTERNAL_TRANSACTION_ID, PARTNER_SITE_NO, BANNER_ID, DEVICE_TYPE, SALES_REWARD, PUBLISHER_REWARD_IN_USD, TOTAL_PRICE_REWARD, TOTAL_PRICE, LOG_DATE, TRANSACTION_ID, SALES_COUNT, PRICE, REWARD_TYPE, COMMISSION_TYPE, AT_COMMISSION, AGENT_COMMISSION, DEFAULT_SALES_COUNT, DEFAULT_PRICE, UUID, PUBLISHER_BONUS, PUBLISHER_BONUS_IN_USD)
VALUES (4, 'publisher1', TO_DATE('2020/08/20', 'YYYY/MM/DD'), TO_DATE('2020/08/20', 'YYYY/MM/DD'), 1, 2, 1, 'internalTransactionId4', 1, 200, 1, 100, 600, 200, 300, SYSDATE, '', 3, 0, 0, 0, 0, 0, 0, 0, '', 120, 0.4);
INSERT INTO SALES_LOG (SEQ_NO, CREATED_BY, SALES_DATE, CONFIRMED_DATE, MERCHANT_CAMPAIGN_NO, RESULT_ID, SALES_LOG_STATUS, INTERNAL_TRANSACTION_ID, PARTNER_SITE_NO, BANNER_ID, DEVICE_TYPE, SALES_REWARD, PUBLISHER_REWARD_IN_USD, TOTAL_PRICE_REWARD, TOTAL_PRICE, LOG_DATE, TRANSACTION_ID, SALES_COUNT, PRICE, REWARD_TYPE, COMMISSION_TYPE, AT_COMMISSION, AGENT_COMMISSION, DEFAULT_SALES_COUNT, DEFAULT_PRICE, UUID, PUBLISHER_BONUS, PUBLISHER_BONUS_IN_USD)
VALUES (5, 'publisher2', TO_DATE('2020/08/20', 'YYYY/MM/DD'), TO_DATE('2020/08/20', 'YYYY/MM/DD'), 1, 2, 1, 'internalTransactionId5', 2, 200, 1, 50, 10, 200, 300, SYSDATE, '', 3, 0, 0, 0, 0, 0, 0, 0, '', 220, 1.2);
INSERT INTO SALES_LOG (SEQ_NO, CREATED_BY, SALES_DATE, CONFIRMED_DATE, MERCHANT_CAMPAIGN_NO, RESULT_ID, SALES_LOG_STATUS, INTERNAL_TRANSACTION_ID, PARTNER_SITE_NO, BANNER_ID, DEVICE_TYPE, SALES_REWARD, PUBLISHER_REWARD_IN_USD, TOTAL_PRICE_REWARD, TOTAL_PRICE, LOG_DATE, TRANSACTION_ID, SALES_COUNT, PRICE, REWARD_TYPE, COMMISSION_TYPE, AT_COMMISSION, AGENT_COMMISSION, DEFAULT_SALES_COUNT, DEFAULT_PRICE, UUID, PUBLISHER_BONUS, PUBLISHER_BONUS_IN_USD)
VALUES (6, 'publisher3', TO_DATE('2020/08/20', 'YYYY/MM/DD'), TO_DATE('2020/08/20', 'YYYY/MM/DD'), 1, 2, 1, 'internalTransactionId6', 3, 200, 1, 100, 30, 200, 300, SYSDATE, '', 3, 0, 0, 0, 0, 0, 0, 0, '', 0, 0);
INSERT INTO SALES_LOG (SEQ_NO, CREATED_BY, SALES_DATE, CONFIRMED_DATE, MERCHANT_CAMPAIGN_NO, RESULT_ID, SALES_LOG_STATUS, INTERNAL_TRANSACTION_ID, PARTNER_SITE_NO, BANNER_ID, DEVICE_TYPE, SALES_REWARD, PUBLISHER_REWARD_IN_USD, TOTAL_PRICE_REWARD, TOTAL_PRICE, LOG_DATE, TRANSACTION_ID, SALES_COUNT, PRICE, REWARD_TYPE, COMMISSION_TYPE, AT_COMMISSION, AGENT_COMMISSION, DEFAULT_SALES_COUNT, DEFAULT_PRICE, UUID, PUBLISHER_BONUS, PUBLISHER_BONUS_IN_USD)
VALUES (7, 'publisher4', TO_DATE('2020/08/20', 'YYYY/MM/DD'), TO_DATE('2020/08/20', 'YYYY/MM/DD'), 1, 2, 1, 'internalTransactionId7', 4, 200, 1, 0, 0, 0, 300, SYSDATE, '', 3, 0, 0, 0, 0, 0, 0, 0, '', 0, 0);

INSERT INTO BANNER_ACCESS_LOG_SUMMARY (CREATED_BY, LOG_DATE, PARTNER_SITE_NO, MERCHANT_CAMPAIGN_NO, BANNER_ID, DEVICE_TYPE, IMPRESSION_COUNT, CLICK_COUNT, CLICK_REWARD, RANK, RESULT_ID, REWARD_TYPE, COMMISSION_TYPE, AT_COMMISSION, AGENT_COMMISSION, CLICK_REWARD_IN_USD)
VALUES ('creativeAccessItemForRewardData', TO_DATE('2017/11/22', 'YYYY/MM/DD'), 10, 2, 200, 1, 5, 10, 100, 5, 2, 0, 0, 0, 0, 100);
INSERT INTO BANNER_ACCESS_LOG_SUMMARY (CREATED_BY, LOG_DATE, PARTNER_SITE_NO, MERCHANT_CAMPAIGN_NO, BANNER_ID, DEVICE_TYPE, IMPRESSION_COUNT, CLICK_COUNT, CLICK_REWARD, RANK, RESULT_ID, REWARD_TYPE, COMMISSION_TYPE, AT_COMMISSION, AGENT_COMMISSION, CLICK_REWARD_IN_USD)
VALUES ('creativeAccessItemForRewardData1', TO_DATE('2020/08/20', 'YYYY/MM/DD'), 1, 1, 200, 1, 1, 1, 100, 5, 2, 0, 0, 0, 0, 200);
INSERT INTO BANNER_ACCESS_LOG_SUMMARY (CREATED_BY, LOG_DATE, PARTNER_SITE_NO, MERCHANT_CAMPAIGN_NO, BANNER_ID, DEVICE_TYPE, IMPRESSION_COUNT, CLICK_COUNT, CLICK_REWARD, RANK, RESULT_ID, REWARD_TYPE, COMMISSION_TYPE, AT_COMMISSION, AGENT_COMMISSION, CLICK_REWARD_IN_USD)
VALUES ('creativeAccessItemForRewardData2', TO_DATE('2020/08/20', 'YYYY/MM/DD'), 2, 1, 200, 1, 2, 2, 300, 5, 2, 0, 0, 0, 0, 50);
INSERT INTO BANNER_ACCESS_LOG_SUMMARY (CREATED_BY, LOG_DATE, PARTNER_SITE_NO, MERCHANT_CAMPAIGN_NO, BANNER_ID, DEVICE_TYPE, IMPRESSION_COUNT, CLICK_COUNT, CLICK_REWARD, RANK, RESULT_ID, REWARD_TYPE, COMMISSION_TYPE, AT_COMMISSION, AGENT_COMMISSION, CLICK_REWARD_IN_USD)
VALUES ('creativeAccessItemForRewardData3', TO_DATE('2020/08/20', 'YYYY/MM/DD'), 3, 1, 200, 1, 2, 2, 300, 5, 2, 0, 0, 0, 0, 50);
INSERT INTO BANNER_ACCESS_LOG_SUMMARY (CREATED_BY, LOG_DATE, PARTNER_SITE_NO, MERCHANT_CAMPAIGN_NO, BANNER_ID, DEVICE_TYPE, IMPRESSION_COUNT, CLICK_COUNT, CLICK_REWARD, RANK, RESULT_ID, REWARD_TYPE, COMMISSION_TYPE, AT_COMMISSION, AGENT_COMMISSION, CLICK_REWARD_IN_USD)
VALUES ('creativeAccessItemForRewardData4', TO_DATE('2020/08/20', 'YYYY/MM/DD'), 4, 1, 200, 1, 2, 2, 0, 5, 2, 0, 0, 0, 0, 0);

INSERT INTO PUBLISHER_ACCOUNT_CAMPAIGN_REWARD_HISTORY (ID, CAMPAIGN_CLOSURE_ID, CREATED_BY, REWARD_MONTH, PUBLISHER_ID, CAMPAIGN_ID, AMOUNT, CLICK_COUNT, IMPRESSION_COUNT, SALES_COUNT)
VALUES (1, 1, 'campaignHistoryEntryToDelete', TO_DATE('2017/11', 'YYYY/MM'), 1, 5, 30, 40, 50, 60);
INSERT INTO PUBLISHER_ACCOUNT_CAMPAIGN_REWARD_HISTORY (ID, CAMPAIGN_CLOSURE_ID, CREATED_BY, REWARD_MONTH, PUBLISHER_ID, CAMPAIGN_ID, AMOUNT, CLICK_COUNT, IMPRESSION_COUNT, SALES_COUNT)
VALUES (2, 2, 'campaignHistoryEntryOtherPublisher', TO_DATE('2017/11', 'YYYY/MM'), 2, 5, 30, 40, 50, 60);
INSERT INTO PUBLISHER_ACCOUNT_CAMPAIGN_REWARD_HISTORY (ID, CAMPAIGN_CLOSURE_ID, CREATED_BY, REWARD_MONTH, PUBLISHER_ID, CAMPAIGN_ID, AMOUNT, CLICK_COUNT, IMPRESSION_COUNT, SALES_COUNT)
VALUES (3, 3, 'campaignHistoryEntryOtherMonth', TO_DATE('2017/10', 'YYYY/MM'), 2, 5, 30, 40, 50, 60);

INSERT INTO PUBLISHER_ACCOUNT_PAYMENT_HISTORY (ID, CAMPAIGN_CLOSURE_ID, CREATED_BY, PUBLISHER_ID, REWARD_MONTH)
VALUES(1, 1, 'paymentHistoryEntryToDelete', 1, TO_DATE('2017/11', 'YYYY/MM'));
INSERT INTO PUBLISHER_ACCOUNT_PAYMENT_HISTORY (ID, CAMPAIGN_CLOSURE_ID, CREATED_BY, PUBLISHER_ID, REWARD_MONTH)
VALUES(2, 2, 'paymentHistoryEntryOtherPublisher', 2, TO_DATE('2017/11', 'YYYY/MM'));
INSERT INTO PUBLISHER_ACCOUNT_PAYMENT_HISTORY (ID, CAMPAIGN_CLOSURE_ID, CREATED_BY, PUBLISHER_ID, REWARD_MONTH)
VALUES(3, 3, 'paymentHistoryEntryOtherMonth', 1, TO_DATE('2017/10', 'YYYY/MM'));

INSERT INTO CURRENCY_EXCHANGE_RATE_HISTORY (CURRENCY, QUOTE_CURRENCY, TARGET_MONTH, RATE) VALUES ('USD', 'MYR', TO_DATE('201903', 'YYYYMM'), 4.05);

INSERT INTO CURRENCY_MASTER (CURRENCY, FRACTIONAL_DIGITS) VALUES ('USD', 2);

INSERT INTO MERCHANT_ACCOUNT (ACCOUNT_NO, COUNTRY_CODE)
VALUES (1, 'ID');

INSERT INTO MERCHANT_CAMPAIGN(ACCOUNT_NO, CAMPAIGN_NO)
VALUES (1, 1);

INSERT INTO MONTHLY_CLOSING (CLOSED_MONTH, TARGET_MONTH, TEMPORARY_CLOSING_FLAG, COUNTRY_CODE)
VALUES ('202111', '202112', 1, 'ID');

INSERT INTO BONUS (ID, SITE_ID, CAMPAIGN_ID, PUBLISHER_BONUS, PUBLISHER_BONUS_IN_USD, STATUS, CONFIRMED_DATE)
VALUES (1, 1, 1, 200, 1.4, 1, TO_DATE('2017/10/10', 'YYYY/MM/DD'));
INSERT INTO BONUS (ID, SITE_ID, CAMPAIGN_ID, PUBLISHER_BONUS, PUBLISHER_BONUS_IN_USD, STATUS, CONFIRMED_DATE)
VALUES (2, 2, 1, 430, 2.1, 1, TO_DATE('2017/10/10 130000', 'YYYY/MM/DD HH24MISS'));
INSERT INTO BONUS (ID, SITE_ID, CAMPAIGN_ID, PUBLISHER_BONUS, PUBLISHER_BONUS_IN_USD, STATUS, CONFIRMED_DATE)
VALUES (3, 3, 2, 130, 0.7, 1, TO_DATE('2017/10/10', 'YYYY/MM/DD'));
INSERT INTO BONUS (ID, SITE_ID, CAMPAIGN_ID, PUBLISHER_BONUS, PUBLISHER_BONUS_IN_USD, STATUS, CONFIRMED_DATE)
VALUES (4, 4, 5, 550, 3.2, 1, TO_DATE('2017/10/10', 'YYYY/MM/DD'));
INSERT INTO BONUS (ID, SITE_ID, CAMPAIGN_ID, PUBLISHER_BONUS, PUBLISHER_BONUS_IN_USD, STATUS, CONFIRMED_DATE)
VALUES (5, 5, 1, 280, 1.6, 1, TO_DATE('2017/10/10 130000', 'YYYY/MM/DD HH24MISS'));
INSERT INTO BONUS (ID, SITE_ID, CAMPAIGN_ID, PUBLISHER_BONUS, PUBLISHER_BONUS_IN_USD, STATUS, CONFIRMED_DATE)
VALUES (6, 6, 1, 330, 1.95, 1, TO_DATE('2017/10/10 150000', 'YYYY/MM/DD HH24MISS'));
INSERT INTO BONUS (ID, SITE_ID, CAMPAIGN_ID, PUBLISHER_BONUS, PUBLISHER_BONUS_IN_USD, STATUS, CONFIRMED_DATE)
VALUES (7, 6, 1, 330, 0, 0, TO_DATE('2017/10/10 150000', 'YYYY/MM/DD HH24MISS'));