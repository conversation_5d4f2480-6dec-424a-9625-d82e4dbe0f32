SET DATABASE SQL SYNTAX ORA TRUE;

INSERT INTO CAMPAIGN_CLOSURE(ID, <PERSON>MPAIGN_ID, STATUS, CLOSED_FROM, CLOSED_TO, IS_PAYMENT_GENERATED)
VALUES(1, 10, 0, TO_DATE('20211001', 'YYYYMMDD'), TO_DATE('20211015', 'YYYYMMDD'), 0);
INSERT INTO CAMPAIGN_CLOSURE(ID, CAMPAIGN_ID, STATUS, CLOSED_FROM, CLOSED_TO, IS_PAYMENT_GENERATED)
VALUES(2, 10, 2, TO_DATE('********', 'YYYYMMDD'), TO_DATE('********', 'YYYYMMDD'), 0);
INSERT INTO CAMPAIGN_CLOSURE(ID, CAMPAIGN_ID, STATUS, CLOSED_FROM, CLOSED_TO, IS_PAYMENT_GENERATED)
VALUES(3, 1, 2, TO_DATE('********', 'YYYYMMDD'), TO_DATE('********', 'YYYYMMDD'), 0);
INSERT INTO CAMPAIGN_CLOSURE(ID, CAMPAIGN_ID, STATUS, CLOSED_FROM, CLOSED_TO, IS_PAYMENT_GENERATED)
VALUES(4, 10, 2, TO_DATE('********', 'YYYYMMDD'), TO_DATE('********', 'YYYYMMDD'), 0);
INSERT INTO CAMPAIGN_CLOSURE(ID, CAMPAIGN_ID, STATUS, CLOSED_FROM, CLOSED_TO, IS_PAYMENT_GENERATED)
VALUES(5, 1, 2, TO_DATE('********', 'YYYYMMDD'), TO_DATE('********', 'YYYYMMDD'), 0);

INSERT INTO PARTNER_ACCOUNT (ACCOUNT_NO, ACCOUNT_STATE, LOGIN_NAME, APPLIED_DATE, COUNTRY_CODE)
VALUES (1, 1, 'activeIndonesiaPublisher', SYSDATE, 'ID');
INSERT INTO PARTNER_ACCOUNT (ACCOUNT_NO, ACCOUNT_STATE, LOGIN_NAME, APPLIED_DATE, COUNTRY_CODE)
VALUES (2, 1, 'activeSingaporePublisher', SYSDATE, 'ID');
INSERT INTO PARTNER_ACCOUNT (ACCOUNT_NO, ACCOUNT_STATE, LOGIN_NAME, APPLIED_DATE, COUNTRY_CODE)
VALUES (3, 0, 'inactivePublisher', SYSDATE, 'ID');
INSERT INTO PARTNER_ACCOUNT (ACCOUNT_NO, ACCOUNT_STATE, LOGIN_NAME, APPLIED_DATE, COUNTRY_CODE)
VALUES (4, 2, 'deletedPublisher', SYSDATE, 'ID');
INSERT INTO PARTNER_ACCOUNT (ACCOUNT_NO, ACCOUNT_STATE, LOGIN_NAME, APPLIED_DATE, COUNTRY_CODE)
VALUES (5, 1, 'activeSingaporePublisher', SYSDATE, 'SG');
INSERT INTO PARTNER_ACCOUNT (ACCOUNT_NO, ACCOUNT_STATE, LOGIN_NAME, APPLIED_DATE, COUNTRY_CODE)
VALUES (6, 1, 'activeIndonesiaPublisher', SYSDATE, 'ID');

INSERT INTO PARTNER_SITE (SITE_NO, ACCOUNT_NO, SITE_NAME, URL, DESCRIPTION, SITE_TYPE, SITE_STATE, CATEGORY_LOW_ID1, CATEGORY_LOW_ID2, CATEGORY_LOW_ID3, MAIN_SITE_FLAG, POINTBACK_FLAG, ALL_BANNERS_FLG, CREATED_BY, CREATED_ON, UPDATED_BY, UPDATED_ON)
VALUES (40, 1, 'siteForIndonesiaRewardData', '', '', 2, 0, 0, 0, 0, 0, 0, 0, NULL, NULL, NULL, NULL);
INSERT INTO PARTNER_SITE (SITE_NO, ACCOUNT_NO, SITE_NAME, URL, DESCRIPTION, SITE_TYPE, SITE_STATE, CATEGORY_LOW_ID1, CATEGORY_LOW_ID2, CATEGORY_LOW_ID3, MAIN_SITE_FLAG, POINTBACK_FLAG, ALL_BANNERS_FLG, CREATED_BY, CREATED_ON, UPDATED_BY, UPDATED_ON)
VALUES (41, 2, 'siteForSingaporeRewardData', '', '', 2, 0, 0, 0, 0, 0, 0, 0, NULL, NULL, NULL, NULL);
INSERT INTO PARTNER_SITE (SITE_NO, ACCOUNT_NO, SITE_NAME, URL, DESCRIPTION, SITE_TYPE, SITE_STATE, CATEGORY_LOW_ID1, CATEGORY_LOW_ID2, CATEGORY_LOW_ID3, MAIN_SITE_FLAG, POINTBACK_FLAG, ALL_BANNERS_FLG, CREATED_BY, CREATED_ON, UPDATED_BY, UPDATED_ON)
VALUES (20, 3, 'siteForInactivePublisher', '', '', 2, 0, 0, 0, 0, 0, 0, 0, NULL, NULL, NULL, NULL);
INSERT INTO PARTNER_SITE (SITE_NO, ACCOUNT_NO, SITE_NAME, URL, DESCRIPTION, SITE_TYPE, SITE_STATE, CATEGORY_LOW_ID1, CATEGORY_LOW_ID2, CATEGORY_LOW_ID3, MAIN_SITE_FLAG, POINTBACK_FLAG, ALL_BANNERS_FLG, CREATED_BY, CREATED_ON, UPDATED_BY, UPDATED_ON)
VALUES (30, 4, 'siteForDeletedPublisher', '', '', 2, 0, 0, 0, 0, 0, 0, 0, NULL, NULL, NULL, NULL);
INSERT INTO PARTNER_SITE (SITE_NO, ACCOUNT_NO, SITE_NAME, URL, DESCRIPTION, SITE_TYPE, SITE_STATE, CATEGORY_LOW_ID1, CATEGORY_LOW_ID2, CATEGORY_LOW_ID3, MAIN_SITE_FLAG, POINTBACK_FLAG, ALL_BANNERS_FLG, CREATED_BY, CREATED_ON, UPDATED_BY, UPDATED_ON)
VALUES (50, 5, 'siteForSingaporeRewardData', '', '', 2, 0, 0, 0, 0, 0, 0, 0, NULL, NULL, NULL, NULL);
INSERT INTO PARTNER_SITE (SITE_NO, ACCOUNT_NO, SITE_NAME, URL, DESCRIPTION, SITE_TYPE, SITE_STATE, CATEGORY_LOW_ID1, CATEGORY_LOW_ID2, CATEGORY_LOW_ID3, MAIN_SITE_FLAG, POINTBACK_FLAG, ALL_BANNERS_FLG, CREATED_BY, CREATED_ON, UPDATED_BY, UPDATED_ON)
VALUES (6, 6, 'siteForIndonesiaRewardData', '', '', 2, 0, 0, 0, 0, 0, 0, 0, NULL, NULL, NULL, NULL);
INSERT INTO PARTNER_SITE (SITE_NO, ACCOUNT_NO)
VALUES (1, 1);

INSERT INTO PUBLISHER_ACCOUNT_CAMPAIGN_REWARD_HISTORY (ID, CAMPAIGN_CLOSURE_ID, CREATED_BY, REWARD_MONTH, PUBLISHER_ID, CAMPAIGN_ID, AMOUNT, CLICK_COUNT, IMPRESSION_COUNT, SALES_COUNT, CREATED_ON, AMOUNT_IN_USD)
VALUES (1, 1, 'PublisherPaymentCalculatorBatch', TO_DATE('201901', 'YYYYMM'), 1, 140, 30, 40, 50, 60, TO_DATE('201901', 'YYYYMM'), 60);
INSERT INTO PUBLISHER_ACCOUNT_CAMPAIGN_REWARD_HISTORY (ID, CAMPAIGN_CLOSURE_ID, CREATED_BY, REWARD_MONTH, PUBLISHER_ID, CAMPAIGN_ID, AMOUNT, CLICK_COUNT, IMPRESSION_COUNT, SALES_COUNT, CREATED_ON, AMOUNT_IN_USD)
VALUES (2, 2,'PublisherPaymentCalculatorBatch', TO_DATE('201901', 'YYYYMM'), 2, 141, 30, 40, 50, 60, TO_DATE('201901', 'YYYYMM'), 60);
INSERT INTO PUBLISHER_ACCOUNT_CAMPAIGN_REWARD_HISTORY (ID, CAMPAIGN_CLOSURE_ID, CREATED_BY, REWARD_MONTH, PUBLISHER_ID, CAMPAIGN_ID, AMOUNT, CLICK_COUNT, IMPRESSION_COUNT, SALES_COUNT, CREATED_ON, AMOUNT_IN_USD)
VALUES (3, 3, 'PublisherPaymentCalculatorBatch', TO_DATE('201901', 'YYYYMM'), 3, 5, 30, 40, 50, 60, TO_DATE('201901', 'YYYYMM'), 60);
INSERT INTO PUBLISHER_ACCOUNT_CAMPAIGN_REWARD_HISTORY (ID, CAMPAIGN_CLOSURE_ID, CREATED_BY, REWARD_MONTH, PUBLISHER_ID, CAMPAIGN_ID, AMOUNT, CLICK_COUNT, IMPRESSION_COUNT, SALES_COUNT, CREATED_ON, AMOUNT_IN_USD)
VALUES (4, 4, 'PublisherPaymentCalculatorBatch', TO_DATE('201902', 'YYYYMM'), 2, 10, 35, 45, 55, 65, TO_DATE('201901', 'YYYYMM'), 70);

INSERT INTO PUBLISHER_ACCOUNT_PAYMENT_HISTORY (ID, CAMPAIGN_CLOSURE_ID, CREATED_BY, PUBLISHER_ID, REWARD_MONTH, CREATED_ON)
VALUES(1, 1,'PublisherPaymentCalculatorBatch', 1, TO_DATE('201901', 'YYYYMM'), TO_DATE('201901', 'YYYYMM'));
INSERT INTO PUBLISHER_ACCOUNT_PAYMENT_HISTORY (ID, CAMPAIGN_CLOSURE_ID, CREATED_BY, PUBLISHER_ID, REWARD_MONTH, CREATED_ON)
VALUES(2, 2, 'PublisherPaymentCalculatorBatch', 2, TO_DATE('201901', 'YYYYMM'), TO_DATE('201901', 'YYYYMM'));
INSERT INTO PUBLISHER_ACCOUNT_PAYMENT_HISTORY (ID, CAMPAIGN_CLOSURE_ID, CREATED_BY, PUBLISHER_ID, REWARD_MONTH, CREATED_ON)
VALUES(3, 3, 'PublisherPaymentCalculatorBatch', 3, TO_DATE('201901', 'YYYYMM'), TO_DATE('201901', 'YYYYMM'));
INSERT INTO PUBLISHER_ACCOUNT_PAYMENT_HISTORY (ID, CAMPAIGN_CLOSURE_ID, CREATED_BY, PUBLISHER_ID, REWARD_MONTH, CREATED_ON)
VALUES(4, 4, 'PublisherPaymentCalculatorBatch', 2, TO_DATE('201902', 'YYYYMM'), TO_DATE('201901', 'YYYYMM'));

INSERT INTO CURRENCY_EXCHANGE_RATE_HISTORY (CURRENCY, QUOTE_CURRENCY, TARGET_MONTH, RATE)
VALUES ('USD', 'IDR', TO_DATE('201901', 'YYYYMM'), 14160.80);
INSERT INTO CURRENCY_EXCHANGE_RATE_HISTORY (CURRENCY, QUOTE_CURRENCY, TARGET_MONTH, RATE)
VALUES ('USD', 'SGD', TO_DATE('201901', 'YYYYMM'), 1.36);

INSERT INTO CURRENCY_MASTER (CURRENCY, FRACTIONAL_DIGITS)
VALUES ('IDR', 2);
INSERT INTO CURRENCY_MASTER (CURRENCY, FRACTIONAL_DIGITS)
VALUES ('SGD', 2);

INSERT INTO SALES_LOG (SEQ_NO, CREATED_BY, SALES_DATE, CONFIRMED_DATE, MERCHANT_CAMPAIGN_NO, RESULT_ID, SALES_LOG_STATUS, INTERNAL_TRANSACTION_ID, PARTNER_SITE_NO, BANNER_ID, DEVICE_TYPE, SALES_REWARD, TOTAL_PRICE_REWARD, TOTAL_PRICE, LOG_DATE, TRANSACTION_ID, SALES_COUNT, PRICE, REWARD_TYPE, COMMISSION_TYPE, AT_COMMISSION, AGENT_COMMISSION, DEFAULT_SALES_COUNT, DEFAULT_PRICE, UUID, PUBLISHER_REWARD_IN_USD, PUBLISHER_BONUS, PUBLISHER_BONUS_IN_USD)
VALUES (4, 'publisher1', TO_DATE('2019/01/20', 'YYYY/MM/DD'), TO_DATE('2019/01/20', 'YYYY/MM/DD'), 10, 2, 1, 'internalTransactionId3', 6, 200, 1, 100, 200, 300, SYSDATE, '', 3, 0, 0, 0, 0, 0, 0, 0, '', 800, 200, 1.2);
INSERT INTO SALES_LOG (SEQ_NO, CREATED_BY, SALES_DATE, CONFIRMED_DATE, MERCHANT_CAMPAIGN_NO, RESULT_ID, SALES_LOG_STATUS, INTERNAL_TRANSACTION_ID, PARTNER_SITE_NO, BANNER_ID, DEVICE_TYPE, SALES_REWARD, TOTAL_PRICE_REWARD, TOTAL_PRICE, LOG_DATE, TRANSACTION_ID, SALES_COUNT, PRICE, REWARD_TYPE, COMMISSION_TYPE, AT_COMMISSION, AGENT_COMMISSION, DEFAULT_SALES_COUNT, DEFAULT_PRICE, UUID, PUBLISHER_REWARD_IN_USD, PUBLISHER_BONUS, PUBLISHER_BONUS_IN_USD)
VALUES (5, 'publisher1', TO_DATE('2019/01/20', 'YYYY/MM/DD'), TO_DATE('2019/01/20', 'YYYY/MM/DD'), 10, 2, 1, 'internalTransactionId5', 6, 200, 1, 100, 200, 300, SYSDATE, '', 3, 0, 0, 0, 0, 0, 0, 0, '', null, 300, 1.9);

INSERT INTO BANNER_ACCESS_LOG_SUMMARY (CREATED_BY, LOG_DATE, PARTNER_SITE_NO, MERCHANT_CAMPAIGN_NO, BANNER_ID, DEVICE_TYPE, IMPRESSION_COUNT, CLICK_COUNT, CLICK_REWARD, RANK, RESULT_ID, REWARD_TYPE, COMMISSION_TYPE, AT_COMMISSION, AGENT_COMMISSION, CLICK_REWARD_IN_USD)
VALUES ('creativeAccessItemForRewardData1', TO_DATE('2019/01/20', 'YYYY/MM/DD'), 40, 140, 200, 1, 1, 1, 100, 5, 2, 0, 0, 0, 0, 200);
INSERT INTO BANNER_ACCESS_LOG_SUMMARY (CREATED_BY, LOG_DATE, PARTNER_SITE_NO, MERCHANT_CAMPAIGN_NO, BANNER_ID, DEVICE_TYPE, IMPRESSION_COUNT, CLICK_COUNT, CLICK_REWARD, RANK, RESULT_ID, REWARD_TYPE, COMMISSION_TYPE, AT_COMMISSION, AGENT_COMMISSION, CLICK_REWARD_IN_USD)
VALUES ('creativeAccessItemForRewardData1', TO_DATE('2019/01/20', 'YYYY/MM/DD'), 40, 141, 200, 1, 1, 1, 100, 5, 2, 0, 0, 0, 0, null)
INSERT INTO BANNER_ACCESS_LOG_SUMMARY (CREATED_BY, LOG_DATE, PARTNER_SITE_NO, MERCHANT_CAMPAIGN_NO, BANNER_ID, DEVICE_TYPE, IMPRESSION_COUNT, CLICK_COUNT, CLICK_REWARD, RANK, RESULT_ID, REWARD_TYPE, COMMISSION_TYPE, AT_COMMISSION, AGENT_COMMISSION, CLICK_REWARD_IN_USD)
VALUES ('creativeAccessItemForRewardData1', TO_DATE('2019/01/20', 'YYYY/MM/DD'), 6, 10, 300, 1, 1, 1, 300, 5, 2, 0, 0, 0, 0, 200);

INSERT INTO MERCHANT_ACCOUNT (ACCOUNT_NO, COUNTRY_CODE)
VALUES (1, 'ID');

INSERT INTO MERCHANT_CAMPAIGN(ACCOUNT_NO, CAMPAIGN_NO)
VALUES (1, 140);
INSERT INTO MERCHANT_CAMPAIGN(ACCOUNT_NO, CAMPAIGN_NO)
VALUES (1, 141);
INSERT INTO MERCHANT_CAMPAIGN(ACCOUNT_NO, CAMPAIGN_NO)
VALUES (1, 10);

INSERT INTO MONTHLY_CLOSING (CLOSED_MONTH, TARGET_MONTH, TEMPORARY_CLOSING_FLAG, COUNTRY_CODE)
VALUES ('202111', '202112', 1, 'ID');

INSERT INTO BONUS (ID, SITE_ID, CAMPAIGN_ID, PUBLISHER_BONUS, PUBLISHER_BONUS_IN_USD, STATUS, CONFIRMED_DATE)
VALUES (1, 6, 10, 200, 1.4, 1, TO_DATE('2019/01/23', 'YYYY/MM/DD'));
INSERT INTO BONUS (ID, SITE_ID, CAMPAIGN_ID, PUBLISHER_BONUS, PUBLISHER_BONUS_IN_USD, STATUS, CONFIRMED_DATE)
VALUES (2, 6, 10, 430, 2.1, 1, TO_DATE('2019/01/27', 'YYYY/MM/DD'));

INSERT INTO COUNTRY (CODE, CURRENCY) VALUES ('ID', 'IDR');
INSERT INTO COUNTRY (CODE, CURRENCY) VALUES ('MY', 'MYR');
INSERT INTO COUNTRY (CODE, CURRENCY) VALUES ('SG', 'SGD');
