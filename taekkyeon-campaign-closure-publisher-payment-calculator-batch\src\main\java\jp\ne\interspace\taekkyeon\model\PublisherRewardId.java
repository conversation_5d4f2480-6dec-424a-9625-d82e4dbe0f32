/**
 * Copyright © 2021 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.model;

import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;

/**
 * DTO for holding the publisher reward ID.
 *
 * <AUTHOR>
 */
@Getter @AllArgsConstructor @EqualsAndHashCode
public class PublisherRewardId {

    private Long campaignClosureId;
    private Long campaignId;
    private String merchantCountryCode;
    private Long publisherId;
    private String publisherCountryCode;
}
