/**
 * Copyright © 2021 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper;

import java.math.BigDecimal;
import java.time.YearMonth;
import java.time.ZonedDateTime;
import java.util.List;

import org.apache.ibatis.annotations.Arg;
import org.apache.ibatis.annotations.ConstructorArgs;
import org.apache.ibatis.annotations.Select;

import jp.ne.interspace.taekkyeon.model.IntegrationTestAccountPaymentHistory;
import jp.ne.interspace.taekkyeon.model.IntegrationTestCampaignRewardHistory;
import jp.ne.interspace.taekkyeon.model.PaymentStatus;
import jp.ne.interspace.taekkyeon.multiline.Multiline;
import jp.ne.interspace.taekkyeon.persist.aws.rdbms.type.handler.TimestampYearMonthTypeHandler;
import jp.ne.interspace.taekkyeon.persist.aws.rdbms.type.handler.UtcZonedDateTimeTypeHandler;

/**
 * MyBatis mapper for handling integration test data operations.
 *
 * <AUTHOR>
 */
public interface IntegrationTestMapper {

    /**
        SELECT
            campaign_closure_id campaignClosureId,
            publisher_id publisherId,
            campaign_id campaignId,
            reward_month rewardMonth,
            impression_count impressionCount,
            click_count clickCount,
            sales_count salesCount,
            amount reward,
            amount_in_usd rewardInUsd,
            conversion_bonus conversionBonus,
            conversion_bonus_in_usd conversionBonusInUsd,
            fixed_bonus fixedBonus,
            fixed_bonus_in_usd fixedBonusInUsd,
            created_by createdBy,
            created_on createdOn
        FROM
            publisher_account_campaign_reward_history
        ORDER BY publisherId,
                 campaignId
     */
    @Multiline String FIND_ALL_CAMPAIGN_REWARD_HISTORY = "";

    /**
     * Returns the all campaign reward history.
     *
     * @return the all campaign reward history
     */
    @Select(FIND_ALL_CAMPAIGN_REWARD_HISTORY)
    @ConstructorArgs({ @Arg(column = "campaignClosureId", javaType = Long.class),
            @Arg(column = "publisherId", javaType = Long.class),
            @Arg(column = "campaignId", javaType = Long.class),
            @Arg(column = "rewardMonth", javaType = YearMonth.class,
                    typeHandler = TimestampYearMonthTypeHandler.class),
            @Arg(column = "impressionCount", javaType = Long.class),
            @Arg(column = "clickCount", javaType = Long.class),
            @Arg(column = "salesCount", javaType = Long.class),
            @Arg(column = "reward", javaType = BigDecimal.class),
            @Arg(column = "rewardInUsd", javaType = BigDecimal.class),
            @Arg(column = "conversionBonus", javaType = BigDecimal.class),
            @Arg(column = "conversionBonusInUsd", javaType = BigDecimal.class),
            @Arg(column = "fixedBonus", javaType = BigDecimal.class),
            @Arg(column = "fixedBonusInUsd", javaType = BigDecimal.class),
            @Arg(column = "createdBy", javaType = String.class),
            @Arg(column = "createdOn", javaType = ZonedDateTime.class,
                typeHandler = UtcZonedDateTimeTypeHandler.class) })
    List<IntegrationTestCampaignRewardHistory> findAllCampaignRewardHistories();

    /**
        SELECT
            campaign_closure_id campaignClosureId,
            publisher_id publisherId,
            reward_month rewardMonth,
            amount reward,
            amount_in_usd rewardInUsd,
            payment_state paymentStatus,
            request_date requestDate,
            created_by createdBy,
            created_on createdOn
        FROM
            publisher_account_payment_history
        ORDER BY publisherId,
                 created_on
     */
    @Multiline String FIND_ALL_PAYMENT_HISTORY = "";

    /**
     * Returns the all payment history.
     *
     * @return the all payment history
     */
    @Select(FIND_ALL_PAYMENT_HISTORY)
    @ConstructorArgs({ @Arg(column = "campaignClosureId", javaType = Long.class),
            @Arg(column = "publisherId", javaType = Long.class),
            @Arg(column = "rewardMonth", javaType = YearMonth.class,
                    typeHandler = TimestampYearMonthTypeHandler.class),
            @Arg(column = "reward", javaType = BigDecimal.class),
            @Arg(column = "rewardInUsd", javaType = BigDecimal.class),
            @Arg(column = "paymentStatus", javaType = PaymentStatus.class),
            @Arg(column = "createdBy", javaType = String.class),
            @Arg(column = "createdOn", javaType = ZonedDateTime.class,
                typeHandler = UtcZonedDateTimeTypeHandler.class) })
    List<IntegrationTestAccountPaymentHistory> findAllAccountPaymentHistories();
}
