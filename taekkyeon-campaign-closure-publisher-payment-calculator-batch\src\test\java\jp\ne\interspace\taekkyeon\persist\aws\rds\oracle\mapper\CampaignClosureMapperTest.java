/**
 * Copyright © 2021 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper;

import java.time.LocalDateTime;
import java.util.List;

import com.google.inject.Inject;

import org.junit.Test;
import org.junit.runner.RunWith;

import jp.ne.interspace.taekkyeon.junit.TaekkyeonHsqldbJunitRunner;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonHsqldbOracleJunitModule;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonModules;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonPropertiesJunitModule;
import jp.ne.interspace.taekkyeon.model.CampaignClosure;

import static jp.ne.interspace.taekkyeon.model.PaymentGenerationStatus.IS_PROCESSING;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertSame;

/**
 * Integration test for {@link CampaignClosureMapper}.
 *
 * <AUTHOR>
 */
@RunWith(TaekkyeonHsqldbJunitRunner.class)
@TaekkyeonModules({ TaekkyeonPropertiesJunitModule.class,
        TaekkyeonHsqldbOracleJunitModule.class })
public class CampaignClosureMapperTest {

    private static final LocalDateTime CLOSED_FROM = LocalDateTime.of(2021, 11, 1, 0, 0);
    private static final LocalDateTime CLOSED_TO = LocalDateTime.of(2021, 11, 15, 0, 0);
    private static final String COUNTRY_CODE = "ID";

    @Inject
    private CampaignClosureMapper underTest;

    @Test
    public void testFindCampaignClosuresByCloseTemporaryFlagShouldReturnCorrectCampaignClosureDataWhenCalled() {
        // when
        List<CampaignClosure> actual = underTest
                .findCampaignClosuresByCloseTemporaryFlag();

        // then
        assertEquals(2, actual.size());
        assertCampaignClosure(actual.get(0), 3L, 1L, CLOSED_FROM, CLOSED_TO);
        assertCampaignClosure(actual.get(1), 4L, 1L, LocalDateTime.of(2022, 7, 1, 0, 0),
                LocalDateTime.of(2022, 7, 15, 0, 0));
    }

    @Test
    public void testFindCampaignClosuresByShouldReturnCorrectCampaignClosureDataWhenCalled() {
        // when
        List<CampaignClosure> actual = underTest.findCampaignClosuresBy(COUNTRY_CODE);

        // then
        assertEquals(2, actual.size());
        assertCampaignClosure(actual.get(0), 3L, 1L, CLOSED_FROM, CLOSED_TO);
        assertCampaignClosure(actual.get(1), 4L, 1L, LocalDateTime.of(2022, 7, 1, 0, 0),
                LocalDateTime.of(2022, 7, 15, 0, 0));
    }

    @Test
    public void testUpdateIsPaymentGeneratedShouldReturnUpdateDataWhenCalled() {
        // when
        int actual = underTest.updateIsPaymentGenerated(4, IS_PROCESSING);

        // then
        assertEquals(1, actual);
    }

    private void assertCampaignClosure(CampaignClosure actual, long expectedId,
            long expectedCampaignId, LocalDateTime expectedClosedFrom,
            LocalDateTime expectedClosedTo) {
        assertNotNull(actual);
        assertSame(expectedCampaignId, actual.getCampaignId());
        assertEquals(expectedClosedFrom, actual.getClosedFrom());
        assertEquals(expectedClosedTo, actual.getClosedTo());
        assertSame(expectedId, actual.getId());
    }
}
