/**
 * Copyright © 2022 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

import org.apache.ibatis.annotations.Arg;
import org.apache.ibatis.annotations.ConstructorArgs;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import jp.ne.interspace.taekkyeon.model.PublisherFixedBonusDetails;
import jp.ne.interspace.taekkyeon.multiline.Multiline;

/**
 * Mybat<PERSON> mapper for publisher bonus.
 *
 * <AUTHOR>
 */
public interface BonusMapper {

    /**
        SELECT
            pa.account_no AS publisherId,
            #{campaignClosureId} AS campaignClosureId,
            b.campaign_id AS campaignId,
            pa.country_code AS publisherCountryCode,
            ma.country_code AS merchantCountryCode,
            SUM(b.publisher_bonus) fixedBonus,
            SUM(b.publisher_bonus_in_usd) fixedBonusInUsd
        FROM
            bonus b
        INNER JOIN
            partner_site ps
        ON
            ps.site_no = b.site_id
        INNER JOIN
            partner_account pa
        ON
            pa.account_no = ps.account_no
        INNER JOIN
            merchant_campaign mc
        ON
            mc.campaign_no = b.campaign_id
        INNER JOIN
            merchant_account ma
        ON
            ma.account_no = mc.account_no
        WHERE
            b.status = 1
        AND
            b.campaign_id = #{campaignId}
        AND
            b.confirmed_date >= #{closedFrom, jdbcType=DATE,
            typeHandler = jp.ne.interspace.taekkyeon.persist.aws.rdbms.type.handler.LocalDateTimeTypeHandler}
        AND
            b.confirmed_date <= #{closedTo, jdbcType=DATE,
            typeHandler = jp.ne.interspace.taekkyeon.persist.aws.rdbms.type.handler.LocalDateTimeTypeHandler}
        AND
            b.publisher_bonus > 0
        GROUP BY
            pa.account_no,
            b.campaign_id,
            pa.country_code,
            ma.country_code
     */
    @Multiline String SELECT_PUBLISHER_BONUS_DETAILS = "";

    /**
     * Returns publisher bonus based on the given condition.
     *
     * @param closedFrom
     *            the given closed from
     * @param closedTo
     *            the given closed to
     * @param campaignId
     *            the given campaign ID
     * @param campaignClosureId
     *            the given campaign closure ID
     * @return bonus publisher rewards based on the given condition
     */
    @Select(SELECT_PUBLISHER_BONUS_DETAILS)
    @ConstructorArgs({ @Arg(column = "publisherId", javaType = Long.class),
            @Arg(column = "campaignClosureId", javaType = Long.class),
            @Arg(column = "campaignId", javaType = Long.class),
            @Arg(column = "publisherCountryCode", javaType = String.class),
            @Arg(column = "merchantCountryCode", javaType = String.class),
            @Arg(column = "fixedBonus", javaType = BigDecimal.class),
            @Arg(column = "fixedBonusInUsd", javaType = BigDecimal.class) })
    List<PublisherFixedBonusDetails> findPublisherBonusesBy(
            @Param("closedFrom") LocalDateTime closedFrom,
            @Param("closedTo") LocalDateTime closedTo,
            @Param("campaignId") Long campaignId,
            @Param("campaignClosureId") Long campaignClosureId);
}
