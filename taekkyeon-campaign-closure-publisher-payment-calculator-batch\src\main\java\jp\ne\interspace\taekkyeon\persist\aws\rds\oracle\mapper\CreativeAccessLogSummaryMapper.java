/**
 * Copyright © 2021 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

import org.apache.ibatis.annotations.Arg;
import org.apache.ibatis.annotations.ConstructorArgs;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import jp.ne.interspace.taekkyeon.model.PublisherRewardCreativeAccessDetails;
import jp.ne.interspace.taekkyeon.multiline.Multiline;

/**
 * Mybat<PERSON> mapper for getting creatives access log summary data.
 *
 * <AUTHOR>
 */
public interface CreativeAccessLogSummaryMapper {

    /**
        SELECT
            pa.account_no AS publisherId,
            #{campaignClosureId} AS campaignClosureId,
            bals.merchant_campaign_no AS campaignId,
            SUM(bals.click_count) AS clickCount,
            SUM(bals.impression_count) AS impressionCount,
            SUM(bals.click_reward) AS clickReward,
            SUM(bals.click_reward_in_usd) AS clickRewardInUsd,
            pa.country_code AS publisherCountryCode,
            ma.country_code AS merchantCountryCode
        FROM
            banner_access_log_summary bals
        INNER JOIN
            partner_site ps
        ON
            ps.site_no = bals.partner_site_no
        INNER JOIN
            partner_account pa
        ON
            pa.account_no = ps.account_no
        INNER JOIN
            merchant_campaign mc
        ON
            mc.campaign_no = bals.merchant_campaign_no
        INNER JOIN
            merchant_account ma
        ON
            ma.account_no = mc.account_no
        WHERE
            bals.merchant_campaign_no = #{campaignId}
        AND
            bals.log_date >= #{closedFrom, jdbcType=DATE,
            typeHandler = jp.ne.interspace.taekkyeon.persist.aws.rdbms.type.handler.LocalDateTimeTypeHandler}
        AND
            bals.log_date <= #{closedTo, jdbcType=DATE,
            typeHandler = jp.ne.interspace.taekkyeon.persist.aws.rdbms.type.handler.LocalDateTimeTypeHandler}
        GROUP BY
            pa.account_no,
            bals.merchant_campaign_no,
            pa.country_code,
            ma.country_code
     */
    @Multiline String SELECT_PUBLISHER_REWARD_CREATIVE_ACCESS_DETAILS = "";

    /**
     * Returns creative access publisher rewards based on the given condition.
     *
     * @param closedFrom
     *          the given closed from
     * @param closedTo
     *          the given closed to
     * @param campaignId
     *          the given campaign id
     * @param campaignClosureId
     *          the given campaign closure id
     * @return creative access publisher rewards based on the given condition
     */
    @Select(SELECT_PUBLISHER_REWARD_CREATIVE_ACCESS_DETAILS)
    @ConstructorArgs({ @Arg(column = "publisherId", javaType = Long.class),
            @Arg(column = "campaignClosureId", javaType = Long.class),
            @Arg(column = "campaignId", javaType = Long.class),
            @Arg(column = "publisherCountryCode", javaType = String.class),
            @Arg(column = "merchantCountryCode", javaType = String.class),
            @Arg(column = "clickCount", javaType = Long.class),
            @Arg(column = "impressionCount", javaType = Long.class),
            @Arg(column = "clickReward", javaType = BigDecimal.class),
            @Arg(column = "clickRewardInUsd", javaType = BigDecimal.class)})
    List<PublisherRewardCreativeAccessDetails> findPublisherRewardsBy(
            @Param("closedFrom") LocalDateTime closedFrom,
            @Param("closedTo") LocalDateTime closedTo,
            @Param("campaignId") Long campaignId,
            @Param("campaignClosureId") Long campaignClosureId);
}
