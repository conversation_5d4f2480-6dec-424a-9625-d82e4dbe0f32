/**
 * Copyright © 2021 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper;

import java.math.BigDecimal;

import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;

import jp.ne.interspace.taekkyeon.multiline.Multiline;

/**
 * MyBatis mapper for the {@code PUBLISHER_ACCOUNT_CAMPAIGN_REWARD_HISTORY} DB table.
 *
 * <AUTHOR>
 */
public interface PublisherAccountCampaignRewardHistoryMapper {

    /**
        INSERT INTO
            publisher_account_campaign_reward_history (
                id,
                campaign_closure_id,
                reward_month,
                publisher_id,
                campaign_id,
                amount,
                amount_in_usd,
                click_count,
                impression_count,
                sales_count,
                conversion_bonus,
                conversion_bonus_in_usd,
                fixed_bonus,
                fixed_bonus_in_usd,
                created_by,
                created_on
        ) VALUES (
            publisher_account_campaign_reward_history_sequence.NEXTVAL,
            #{campaignClosureId},
            TO_DATE(#{targetMonth}, 'YYYYMM'),
            #{publisherId},
            #{campaignId},
            #{reward},
            #{rewardInUsd, jdbcType=NUMERIC},
            #{clickCount},
            #{impressionCount},
            #{salesCount},
            #{conversionBonus},
            #{conversionBonusInUsd},
            #{fixedBonus},
            #{fixedBonusInUsd},
            'PublisherCampaignClosurePaymentCalculatorBatch',
            SYSDATE
        )
     */
    @Multiline String INSERT_HISTORY_ITEM = "";

    /**
     * Inserts a new reward history entry based on the given parameters.
     *
     * @param campaignClosureId
     *            ID of the given campaign closure
     * @param targetMonth
     *            the given month
     * @param publisherId
     *            ID of the given publisher
     * @param campaignId
     *            ID of the given campaign
     * @param reward
     *            the given reward
     * @param rewardInUsd
     *            the given reward in USD
     * @param clickCount
     *            the given click count
     * @param impressionCount
     *            the given impression count
     * @param salesCount
     *            the given sales count
     * @param conversionBonus
     *            the given conversion bonus
     * @param conversionBonusInUsd
     *            the given conversion bonus in usd
     * @param fixedBonus
     *            the given fixed bonus
     * @param fixedBonusInUsd
     *            the given fixed bonus in usd
     * @return the number of inserted rows
     */
    @Insert(INSERT_HISTORY_ITEM)
    int insertHistoryItemBy(@Param("campaignClosureId") long campaignClosureId,
            @Param("targetMonth") String targetMonth,
            @Param("publisherId") Long publisherId, @Param("campaignId") Long campaignId,
            @Param("reward") BigDecimal reward,
            @Param("rewardInUsd") BigDecimal rewardInUsd,
            @Param("clickCount") Long clickCount,
            @Param("impressionCount") Long impressionCount,
            @Param("salesCount") Long salesCount,
            @Param("conversionBonus") BigDecimal conversionBonus,
            @Param("conversionBonusInUsd") BigDecimal conversionBonusInUsd,
            @Param("fixedBonus") BigDecimal fixedBonus,
            @Param("fixedBonusInUsd") BigDecimal fixedBonusInUsd);

    /**
        DELETE FROM
            publisher_account_campaign_reward_history
        WHERE
            campaign_closure_id = #{campaignClosureId}
        AND
            publisher_id = #{publisherId}
     */
    @Multiline String DELETE_BY_CAMPAIGN_CLOSURE_ID_AND_PUBLISHER_ID = "";

    /**
     * Deletes all reward history items for the given publisher in the given campaign
     * closure id.
     *
     * @param publisherId
     *            the given of publisher id
     * @param campaignClosureId
     *            the given of campaign closure id
     * @return the number of deleted rows
     */
    @Delete(DELETE_BY_CAMPAIGN_CLOSURE_ID_AND_PUBLISHER_ID)
    int deleteBy(@Param("publisherId") Long publisherId,
            @Param("campaignClosureId") Long campaignClosureId);
}
