/**
 * Copyright © 2021 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import com.google.common.collect.ImmutableMap;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;

import jp.ne.interspace.taekkyeon.junit.TaekkyeonMockitoJunitRunner;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonModules;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonPropertiesJunitModule;
import jp.ne.interspace.taekkyeon.model.CurrencyExchangeRateCacheKey;
import jp.ne.interspace.taekkyeon.model.PublisherFixedBonusDetails;
import jp.ne.interspace.taekkyeon.model.PublisherRewardConversionDetails;
import jp.ne.interspace.taekkyeon.model.PublisherRewardCreativeAccessDetails;
import jp.ne.interspace.taekkyeon.model.PublisherRewardDetails;
import jp.ne.interspace.taekkyeon.model.PublisherRewardId;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.BonusMapper;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.ConversionMapper;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.CreativeAccessLogSummaryMapper;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.CurrencyExchangeRateMapper;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.CurrencyMapper;

import static java.time.ZoneOffset.UTC;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.INTERMEDIARY_CURRENCY;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertSame;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyZeroInteractions;
import static org.mockito.Mockito.when;

/**
 * Unit test for {@link PublisherRewardService}.
 *
 * <AUTHOR>
 */
@RunWith(TaekkyeonMockitoJunitRunner.class)
@TaekkyeonModules(TaekkyeonPropertiesJunitModule.class)
public class PublisherRewardServiceTest {

    private static final String TARGET_MONTH = "201903";
    private static final YearMonth TARGET_YEAR_MONTH = YearMonth.of(2019, 3);
    private static final ZonedDateTime TARGET_DATE_TIME_FROM = ZonedDateTime.of(2019, 2,
            28, 17, 0, 0, 0, UTC);
    private static final ZonedDateTime TARGET_DATE_TIME_TO = ZonedDateTime.of(2019, 3, 31,
            17, 0, 0, 0, UTC);
    private static final long PUBLISHER_ID1 = 1;
    private static final long PUBLISHER_ID2 = 2;
    private static final long PUBLISHER_ID3 = 3;
    private static final long CAMPAIGN_ID1 = 111;
    private static final long CAMPAIGN_ID2 = 222;
    private static final long CAMPAIGN_ID3 = 333;
    private static final String MERCHANT_COUNTRY_CODE_ID = "ID";
    private static final String PUBLISHER_COUNTRY_CODE_MY = "MY";
    private static final String PUBLISHER_COUNTRY_CODE_SG = "SG";
    private static final String MERCHANT_CURRENCY_IDR = "IDR";
    private static final String PUBLISHER_CURRENCY_IDR = "IDR";
    private static final String PUBLISHER_CURRENCY_MYR = "MYR";
    private static final String PUBLISHER_CURRENCY_SGD = "SGD";
    private static final long SALES_COUNT = 31;
    private static final BigDecimal REWARD = new BigDecimal("200.21");
    private static final BigDecimal REWARD_IN_USD = new BigDecimal("23.46");
    private static final BigDecimal CONVERSION_REWARD = new BigDecimal("123.456");
    private static final BigDecimal CONVERSION_REWARD_IN_USD = new BigDecimal("223.456");
    private static final long CLICK_COUNT1 = 32;
    private static final long CLICK_COUNT2 = 33;
    private static final long IMPRESSION_COUNT1 = 34;
    private static final long IMPRESSION_COUNT2 = 35;
    private static final long DEFAULT_COUNT_VALUE = 0;
    private static final BigDecimal CLICK_REWARD1 = new BigDecimal("123.456");
    private static final BigDecimal CLICK_REWARD2 = new BigDecimal("456.789");
    private static final BigDecimal CLICK_REWARD_IN_USD1 = new BigDecimal("323.456");
    private static final BigDecimal CLICK_REWARD_IN_USD2 = new BigDecimal("856.789");
    private static final BigDecimal AGGREGATED_REWARD = new BigDecimal("246.912");
    private static final BigDecimal AGGREGATED_REWARD_IN_USD = new BigDecimal("546.912");
    private static final LocalDateTime CLOSED_FROM = LocalDateTime.of(2021, 10, 01, 00,
            00);
    private static final LocalDateTime CLOSED_TO = LocalDateTime.of(2021, 10, 05, 00, 00);
    private static final Long CAMPAIGN_CLOSURE_ID = 1L;
    private static final BigDecimal CONVERSION_BONUS1 = new BigDecimal("20.00");
    private static final BigDecimal CONVERSION_BONUS_IN_USD1 = new BigDecimal("0.60");
    private static final BigDecimal FIXED_BONUS1 = new BigDecimal("10.00");
    private static final BigDecimal FIXED_BONUS_IN_USD1 = new BigDecimal("0.1");
    private static final BigDecimal CONVERSION_BONUS2 = new BigDecimal("40.00");
    private static final BigDecimal CONVERSION_BONUS_IN_USD2 = new BigDecimal("1.20");
    private static final BigDecimal FIXED_BONUS2 = new BigDecimal("20.00");
    private static final BigDecimal FIXED_BONUS_IN_USD2 = new BigDecimal("0.2");

    @InjectMocks
    @Spy
    private PublisherRewardService underTest;

    @Mock
    private ConversionMapper conversionMapper;

    @Mock
    private CreativeAccessLogSummaryMapper creativeAccessLogSummaryMapper;

    @Mock
    private CurrencyExchangeRateMapper currencyExchangeRateMapper;

    @Mock
    private CurrencyMapper currencyMapper;

    @Mock
    private BonusMapper bonusMapper;

    @Mock
    private CountryService countryService;

    @Test
    public void testFindPublisherRewardDetailsByShouldReturnCorrectDataWithRdsMode() {
        // given
        List<PublisherRewardConversionDetails> rewardConversionDetails = Collections
                .emptyList();
        when(conversionMapper.findPublisherRewardsBy(CLOSED_FROM, CLOSED_TO, CAMPAIGN_ID1,
                CAMPAIGN_CLOSURE_ID)).thenReturn(rewardConversionDetails);

        List<PublisherRewardCreativeAccessDetails> rewardCreativeAccessDetails = Collections
                .emptyList();
        when(creativeAccessLogSummaryMapper.findPublisherRewardsBy(CLOSED_FROM, CLOSED_TO,
                CAMPAIGN_ID1, CAMPAIGN_CLOSURE_ID))
                        .thenReturn(rewardCreativeAccessDetails);

        List<PublisherFixedBonusDetails> publisherBonusDetails = Collections
                .emptyList();
        when(bonusMapper.findPublisherBonusesBy(CLOSED_FROM, CLOSED_TO,
                CAMPAIGN_ID1, CAMPAIGN_CLOSURE_ID))
                        .thenReturn(publisherBonusDetails);

        List<PublisherRewardDetails> expected = Collections.emptyList();
        doReturn(expected).when(underTest).createPublisherRewardsFrom(CAMPAIGN_CLOSURE_ID,
                rewardConversionDetails, rewardCreativeAccessDetails,
                publisherBonusDetails);

        // when
        List<PublisherRewardDetails> actual = underTest.findPublisherRewardDetailsBy(
                CAMPAIGN_CLOSURE_ID, CAMPAIGN_ID1, CLOSED_FROM, CLOSED_TO);

        // then
        assertSame(expected, actual);
    }

    @Test
    public void testConvertByCurrencyCodeShouldReturnCorrectDataWhenSourceCurrencyAndTargetCurrencyAreDifferent() {
        // given
        int fractionalDigits = 2;
        BigDecimal expected = new BigDecimal("0.01");
        BigDecimal reward = new BigDecimal("50");
        CurrencyExchangeRateCacheKey merchantCacheKey = new CurrencyExchangeRateCacheKey(
                INTERMEDIARY_CURRENCY, MERCHANT_CURRENCY_IDR, TARGET_YEAR_MONTH);
        doReturn(new BigDecimal("14160.80")).when(currencyExchangeRateMapper)
                .findExchangeRateBy(merchantCacheKey);
        CurrencyExchangeRateCacheKey publisherCacheKey = new CurrencyExchangeRateCacheKey(
                INTERMEDIARY_CURRENCY, PUBLISHER_CURRENCY_MYR, TARGET_YEAR_MONTH);
        doReturn(new BigDecimal("4.09")).when(currencyExchangeRateMapper)
                .findExchangeRateBy(publisherCacheKey);
        doReturn(fractionalDigits).when(currencyMapper)
                .findFractionalDigitsBy(PUBLISHER_CURRENCY_MYR);

        // when
        BigDecimal actual = underTest.convertByCurrencyCode(reward, MERCHANT_CURRENCY_IDR,
                PUBLISHER_CURRENCY_MYR, TARGET_MONTH);

        // then
        assertEquals(expected, actual);
    }

    @Test
    public void testConvertByCurrencyCodeShouldReturnCorrectDataWhenSourceCurrencyAndTargetCurrencyAreTheSame() {
        // given
        BigDecimal expected = new BigDecimal("50");

        // when
        BigDecimal actual = underTest.convertByCurrencyCode(expected,
                MERCHANT_CURRENCY_IDR, PUBLISHER_CURRENCY_IDR, TARGET_MONTH);

        // then
        assertEquals(expected, actual);

        verifyZeroInteractions(currencyExchangeRateMapper);
        verifyZeroInteractions(currencyMapper);
    }

    @Test
    public void testCreateDateTimeFromShouldReturnCorrectDateTimeWhenNumberMonthsToAddIsZero() {
        // given
        ZoneId zoneId = ZoneId.of("Asia/Jakarta");

        // when
        ZonedDateTime actual = underTest.createDateTimeFrom(TARGET_MONTH, zoneId, 0);

        // then
        assertEquals(TARGET_DATE_TIME_FROM, actual);
    }

    @Test
    public void testCreateDateTimeFromShouldReturnCorrectDateTimeWhenNumberMonthsToAddIsNotZero() {
        // given
        ZoneId zoneId = ZoneId.of("Asia/Jakarta");

        // when
        ZonedDateTime actual = underTest.createDateTimeFrom(TARGET_MONTH, zoneId, 1);

        // then
        assertEquals(TARGET_DATE_TIME_TO, actual);
    }

    @Test
    public void testCreatePublisherRewardsFromShouldReturnCorrectDataWhenOnlyConversionRewardsExists() {
        // given
        PublisherRewardId publisherRewardId = createPublisherRewardId();
        PublisherRewardDetails expectedReward = createConversionReward();
        Map<PublisherRewardId, PublisherRewardDetails> conversionRewardCache = ImmutableMap
                .of(publisherRewardId, expectedReward);
        List<PublisherRewardConversionDetails> rewardConversionDetails = Arrays
                .asList(new PublisherRewardConversionDetails(publisherRewardId,
                        SALES_COUNT, CONVERSION_REWARD, CONVERSION_REWARD_IN_USD,
                        CONVERSION_BONUS1, CONVERSION_BONUS_IN_USD1));

        List<PublisherRewardCreativeAccessDetails> rewardCreativeAccessDetails = Collections
                .emptyList();
        List<PublisherFixedBonusDetails> publisherBonusDetails = Collections.emptyList();
        Map<PublisherRewardId, PublisherRewardDetails> creativeRewardCache = Collections
                .emptyMap();

        doReturn(conversionRewardCache).when(underTest).createConversionRewardCacheFrom(
                CAMPAIGN_CLOSURE_ID, rewardConversionDetails);

        doReturn(creativeRewardCache).when(underTest).createCreativeRewardCacheFrom(
                CAMPAIGN_CLOSURE_ID, rewardCreativeAccessDetails);

        Map<PublisherRewardId, PublisherRewardDetails> fixedBonusCache = Collections
                .emptyMap();
        doReturn(fixedBonusCache).when(underTest)
                .createBonusRewardCacheFrom(CAMPAIGN_CLOSURE_ID, publisherBonusDetails);

        // when
        List<PublisherRewardDetails> actual = underTest.createPublisherRewardsFrom(
                CAMPAIGN_CLOSURE_ID, rewardConversionDetails, rewardCreativeAccessDetails,
                publisherBonusDetails);

        // then
        assertNotNull(actual);
        assertEquals(1, actual.size());
        assertSame(expectedReward, actual.get(0));

        verify(underTest, never()).mergeConversionRewardAndCreativeReward(anyLong(),
                any(PublisherRewardDetails.class), any(PublisherRewardDetails.class));
    }

    @Test
    public void testCreatePublisherRewardsFromShouldReturnCorrectDataWhenOnlyCreativeRewardsExists() {
        // given
        List<PublisherRewardConversionDetails> rewardConversionDetails = Collections
                .emptyList();
        Map<PublisherRewardId, PublisherRewardDetails> conversionRewardCache = Collections
                .emptyMap();

        PublisherRewardId publisherRewardId = createPublisherRewardId();
        PublisherRewardDetails expectedReward1 = createCreativeReward1();
        PublisherRewardId mongoId2 = createPublisherRewardId2();
        PublisherRewardDetails expectedReward2 = createCreativeReward2();
        Map<PublisherRewardId, PublisherRewardDetails> creativeRewardCache = ImmutableMap
                .of(publisherRewardId, expectedReward1, mongoId2, expectedReward2);
        List<PublisherRewardCreativeAccessDetails> rewardCreativeAccessDetails = Arrays
                .asList(new PublisherRewardCreativeAccessDetails(publisherRewardId,
                        CLICK_COUNT1, IMPRESSION_COUNT1, CLICK_REWARD1,
                        CLICK_REWARD_IN_USD1),
                        new PublisherRewardCreativeAccessDetails(mongoId2, CLICK_COUNT2,
                                IMPRESSION_COUNT2, CLICK_REWARD2, CLICK_REWARD_IN_USD2));

        List<PublisherFixedBonusDetails> publisherBonusDetails = Collections.emptyList();
        Map<PublisherRewardId, PublisherRewardDetails> fixedBonusCache = Collections
                .emptyMap();
        doReturn(conversionRewardCache).when(underTest).createConversionRewardCacheFrom(
                CAMPAIGN_CLOSURE_ID, rewardConversionDetails);

        doReturn(creativeRewardCache).when(underTest).createCreativeRewardCacheFrom(
                CAMPAIGN_CLOSURE_ID, rewardCreativeAccessDetails);

        doReturn(fixedBonusCache).when(underTest).createBonusRewardCacheFrom(
                CAMPAIGN_CLOSURE_ID, publisherBonusDetails);

        // when
        List<PublisherRewardDetails> actual = underTest.createPublisherRewardsFrom(
                CAMPAIGN_CLOSURE_ID, rewardConversionDetails,
                rewardCreativeAccessDetails, publisherBonusDetails);

        // then
        assertNotNull(actual);
        assertEquals(2, actual.size());
        assertSame(expectedReward2, actual.get(1));
        assertSame(expectedReward1, actual.get(0));

        verify(underTest, never()).mergeConversionRewardAndCreativeReward(anyLong(),
                any(PublisherRewardDetails.class), any(PublisherRewardDetails.class));
    }

    @Test
    public void testCreatePublisherRewardsFromShouldReturnCorrectDataWhenConversionRewardsAndCreativeRewardsAndFixedBonusExistsAndRewardsAreAggregated() {
        // given
        PublisherRewardId publisherRewardId = createPublisherRewardId();
        PublisherRewardDetails conversionReward = createConversionReward();
        Map<PublisherRewardId, PublisherRewardDetails> conversionRewardCache = ImmutableMap
                .of(publisherRewardId, conversionReward);
        List<PublisherRewardConversionDetails> rewardConversionDetails = Arrays
                .asList(new PublisherRewardConversionDetails(publisherRewardId,
                        SALES_COUNT, CONVERSION_REWARD, CONVERSION_REWARD_IN_USD,
                        CONVERSION_BONUS1, CONVERSION_BONUS_IN_USD1));

        PublisherRewardId mongoId2 = createPublisherRewardId2();
        PublisherRewardDetails creativeReward = createCreativeReward1();
        PublisherRewardDetails expectedCreativeReward = createCreativeReward2();
        Map<PublisherRewardId, PublisherRewardDetails> creativeRewardCache = ImmutableMap
                .of(publisherRewardId, creativeReward, mongoId2, expectedCreativeReward);
        List<PublisherRewardCreativeAccessDetails> rewardCreativeAccessDetails = Arrays
                .asList(new PublisherRewardCreativeAccessDetails(publisherRewardId,
                        CLICK_COUNT1, IMPRESSION_COUNT1, CLICK_REWARD1,
                        CLICK_REWARD_IN_USD1),
                        new PublisherRewardCreativeAccessDetails(mongoId2, CLICK_COUNT2,
                                IMPRESSION_COUNT2, CLICK_REWARD2, CLICK_REWARD_IN_USD2));

        PublisherRewardDetails fixedBonus = createFixedBonus();
        Map<PublisherRewardId, PublisherRewardDetails> publisherBonusCache = ImmutableMap
                .of(publisherRewardId, fixedBonus);
        List<PublisherFixedBonusDetails> publisherBonusDetails = Arrays
                .asList(new PublisherFixedBonusDetails(publisherRewardId, FIXED_BONUS1,
                        FIXED_BONUS_IN_USD1));

        doReturn(conversionRewardCache).when(underTest).createConversionRewardCacheFrom(
                CAMPAIGN_CLOSURE_ID, rewardConversionDetails);
        doReturn(creativeRewardCache).when(underTest).createCreativeRewardCacheFrom(
                CAMPAIGN_CLOSURE_ID, rewardCreativeAccessDetails);
        doReturn(publisherBonusCache).when(underTest)
                .createBonusRewardCacheFrom(CAMPAIGN_CLOSURE_ID, publisherBonusDetails);

        PublisherRewardDetails expectedAggregatedConversionRewardAndCreativeReward =
                new PublisherRewardDetails(CAMPAIGN_CLOSURE_ID, PUBLISHER_ID1,
                        PUBLISHER_CURRENCY_MYR, CAMPAIGN_ID1, MERCHANT_CURRENCY_IDR,
                        AGGREGATED_REWARD, AGGREGATED_REWARD_IN_USD, CLICK_COUNT1,
                        IMPRESSION_COUNT1, SALES_COUNT, CONVERSION_BONUS1,
                        CONVERSION_BONUS_IN_USD1, BigDecimal.ZERO, BigDecimal.ZERO);
        doReturn(expectedAggregatedConversionRewardAndCreativeReward).when(underTest)
                .mergeConversionRewardAndCreativeReward(CAMPAIGN_CLOSURE_ID,
                        conversionReward, creativeReward);

        PublisherRewardDetails expectedAggregatedReward = new PublisherRewardDetails(
                CAMPAIGN_CLOSURE_ID, PUBLISHER_ID1, PUBLISHER_CURRENCY_MYR, CAMPAIGN_ID1,
                MERCHANT_CURRENCY_IDR, AGGREGATED_REWARD, AGGREGATED_REWARD_IN_USD,
                CLICK_COUNT1, IMPRESSION_COUNT1, SALES_COUNT, CONVERSION_BONUS1,
                CONVERSION_BONUS_IN_USD1, FIXED_BONUS1, FIXED_BONUS_IN_USD1);
        doReturn(expectedAggregatedReward).when(underTest).mergePublisherReward(
                CAMPAIGN_CLOSURE_ID, expectedAggregatedConversionRewardAndCreativeReward,
                fixedBonus);

        // when
        List<PublisherRewardDetails> actual = underTest.createPublisherRewardsFrom(
                CAMPAIGN_CLOSURE_ID, rewardConversionDetails, rewardCreativeAccessDetails,
                publisherBonusDetails);

        // then
        assertNotNull(actual);
        assertEquals(2, actual.size());
        assertSame(expectedCreativeReward, actual.get(1));
        assertSame(expectedAggregatedReward, actual.get(0));
    }

    @Test
    public void testCreatePublisherRewardsFromShouldReturnCorrectDataWhenConversionRewardsAndCreativeRewardsExistsAndNoRewardsAreAggregated() {
        // given
        PublisherRewardId publisherRewardId = createPublisherRewardId();
        PublisherRewardDetails expectedReward1 = createConversionReward();
        Map<PublisherRewardId, PublisherRewardDetails> conversionRewardCache = ImmutableMap
                .of(publisherRewardId, expectedReward1);
        List<PublisherRewardConversionDetails> rewardConversionDetails = Arrays
                .asList(new PublisherRewardConversionDetails(publisherRewardId,
                        SALES_COUNT, CONVERSION_REWARD, CONVERSION_REWARD_IN_USD,
                        CONVERSION_BONUS1, CONVERSION_BONUS_IN_USD1));

        PublisherRewardId mongoId2 = createPublisherRewardId2();
        PublisherRewardDetails expectedReward2 = createCreativeReward2();
        Map<PublisherRewardId, PublisherRewardDetails> creativeRewardCache = ImmutableMap
                .of(mongoId2, expectedReward2);
        List<PublisherRewardCreativeAccessDetails> rewardCreativeAccessDetails = Arrays
                .asList(new PublisherRewardCreativeAccessDetails(mongoId2, CLICK_COUNT2,
                        IMPRESSION_COUNT2, CLICK_REWARD2, CLICK_REWARD_IN_USD2));

        PublisherRewardId publisherRewardId3 = createPublisherRewardId3();
        PublisherRewardDetails expectedBonus3 = createFixedBonus3();
        Map<PublisherRewardId, PublisherRewardDetails> publisherBonusCache = ImmutableMap
                .of(publisherRewardId3, expectedBonus3);
        List<PublisherFixedBonusDetails> publisherBonusDetails = Arrays
                .asList(new PublisherFixedBonusDetails(publisherRewardId3,
                        FIXED_BONUS2, FIXED_BONUS_IN_USD2));

        doReturn(conversionRewardCache).when(underTest).createConversionRewardCacheFrom(
                CAMPAIGN_CLOSURE_ID, rewardConversionDetails);

        doReturn(creativeRewardCache).when(underTest).createCreativeRewardCacheFrom(
                CAMPAIGN_CLOSURE_ID, rewardCreativeAccessDetails);
        doReturn(publisherBonusCache).when(underTest)
                .createBonusRewardCacheFrom(CAMPAIGN_CLOSURE_ID, publisherBonusDetails);

        // when
        List<PublisherRewardDetails> actual = underTest.createPublisherRewardsFrom(
                CAMPAIGN_CLOSURE_ID, rewardConversionDetails,
                rewardCreativeAccessDetails, publisherBonusDetails);

        // then
        assertNotNull(actual);
        assertEquals(2, actual.size());
        assertSame(expectedReward1, actual.get(0));
        assertSame(expectedReward2, actual.get(1));

        verify(underTest, never()).mergeConversionRewardAndCreativeReward(anyLong(),
                any(PublisherRewardDetails.class), any(PublisherRewardDetails.class));
        verify(underTest, never()).mergeConversionRewardAndCreativeReward(anyLong(),
                any(PublisherRewardDetails.class), any(PublisherRewardDetails.class));
    }

    @Test
    public void testCreateConversionRewardCacheFromShouldReturnCorrectDataWhenCalled() {
        // given
        PublisherRewardId publisherRewardId = createPublisherRewardId();
        PublisherRewardDetails reward = createConversionReward();
        Map<PublisherRewardId, PublisherRewardDetails> expected = ImmutableMap
                .of(publisherRewardId, reward);
        List<PublisherRewardConversionDetails> rewardConversionDetails = Arrays
                .asList(new PublisherRewardConversionDetails(publisherRewardId,
                        SALES_COUNT, CONVERSION_REWARD, CONVERSION_REWARD_IN_USD,
                        CONVERSION_BONUS1, CONVERSION_BONUS_IN_USD1));

        doReturn(reward).when(underTest).createPublisherRewardDetailsFrom(
                CAMPAIGN_CLOSURE_ID, publisherRewardId, CONVERSION_REWARD,
                CONVERSION_REWARD_IN_USD, DEFAULT_COUNT_VALUE, DEFAULT_COUNT_VALUE,
                SALES_COUNT, CONVERSION_BONUS1, CONVERSION_BONUS_IN_USD1, BigDecimal.ZERO,
                BigDecimal.ZERO);

        // when
        Map<PublisherRewardId, PublisherRewardDetails> actual = underTest
                .createConversionRewardCacheFrom(CAMPAIGN_CLOSURE_ID,
                        rewardConversionDetails);

        // then
        assertEquals(expected, actual);
    }

    @Test
    public void testCreateCreativeRewardCacheFromShouldReturnCorrectDataWhenCalled() {
        // given
        PublisherRewardId publisherRewardId = createPublisherRewardId();
        PublisherRewardDetails reward = createCreativeReward1();
        Map<PublisherRewardId, PublisherRewardDetails> expected = ImmutableMap
                .of(publisherRewardId, reward);
        List<PublisherRewardCreativeAccessDetails> rewardCreativeAccessDetails = Arrays
                .asList(new PublisherRewardCreativeAccessDetails(publisherRewardId,
                        CLICK_COUNT1, IMPRESSION_COUNT1, CLICK_REWARD1,
                        CLICK_REWARD_IN_USD1));

        doReturn(reward).when(underTest).createPublisherRewardDetailsFrom(
                CAMPAIGN_CLOSURE_ID, publisherRewardId, CLICK_REWARD1,
                CLICK_REWARD_IN_USD1, CLICK_COUNT1, IMPRESSION_COUNT1,
                DEFAULT_COUNT_VALUE, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO,
                BigDecimal.ZERO);

        // when
        Map<PublisherRewardId, PublisherRewardDetails> actual = underTest
                .createCreativeRewardCacheFrom(CAMPAIGN_CLOSURE_ID,
                        rewardCreativeAccessDetails);

        // then
        assertEquals(expected, actual);
    }

    @Test
    public void testCreatePublisherRewardDetailsFromShouldReturnCorrectDataWhenGivenConversionPaymentReward() {
        // given
        PublisherRewardId publisherRewardId = createPublisherRewardId();

        when(countryService.findCurrencyBy(MERCHANT_COUNTRY_CODE_ID))
                .thenReturn(MERCHANT_CURRENCY_IDR);
        when(countryService.findCurrencyBy(PUBLISHER_COUNTRY_CODE_MY))
                .thenReturn(PUBLISHER_CURRENCY_MYR);

        // when
        PublisherRewardDetails actual = underTest.createPublisherRewardDetailsFrom(
                CAMPAIGN_CLOSURE_ID, publisherRewardId, CONVERSION_REWARD,
                CONVERSION_REWARD_IN_USD, DEFAULT_COUNT_VALUE, DEFAULT_COUNT_VALUE,
                SALES_COUNT, CONVERSION_BONUS1, CONVERSION_BONUS_IN_USD1, BigDecimal.ZERO,
                BigDecimal.ZERO);

        // then
        assertPublisherRewardDetails(actual, CAMPAIGN_CLOSURE_ID, PUBLISHER_ID1,
                PUBLISHER_CURRENCY_MYR,
                CAMPAIGN_ID1, MERCHANT_CURRENCY_IDR, CONVERSION_REWARD,
                CONVERSION_REWARD_IN_USD, DEFAULT_COUNT_VALUE, DEFAULT_COUNT_VALUE,
                SALES_COUNT, CONVERSION_BONUS1, CONVERSION_BONUS_IN_USD1, BigDecimal.ZERO,
                BigDecimal.ZERO);
    }

    @Test
    public void testCreatePublisherRewardDetailsFromShouldReturnCorrectDataWhenGivenCreativePaymentReward() {
        // given
        PublisherRewardId publisherRewardId = createPublisherRewardId();

        when(countryService.findCurrencyBy(MERCHANT_COUNTRY_CODE_ID))
                .thenReturn(MERCHANT_CURRENCY_IDR);
        when(countryService.findCurrencyBy(PUBLISHER_COUNTRY_CODE_MY))
                .thenReturn(PUBLISHER_CURRENCY_MYR);

        // when
        PublisherRewardDetails actual = underTest.createPublisherRewardDetailsFrom(
                CAMPAIGN_CLOSURE_ID, publisherRewardId, CLICK_REWARD1,
                CLICK_REWARD_IN_USD1, CLICK_COUNT1, IMPRESSION_COUNT1,
                DEFAULT_COUNT_VALUE, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO,
                BigDecimal.ZERO);

        // then
        assertPublisherRewardDetails(actual, CAMPAIGN_CLOSURE_ID, PUBLISHER_ID1,
                PUBLISHER_CURRENCY_MYR,
                CAMPAIGN_ID1, MERCHANT_CURRENCY_IDR, CLICK_REWARD1, CLICK_REWARD_IN_USD1,
                CLICK_COUNT1, IMPRESSION_COUNT1, DEFAULT_COUNT_VALUE, BigDecimal.ZERO,
                BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO);
    }

    @Test
    public void testCreatePublisherRewardDetailsFromShouldReturnCorrectDataWhenGivenFixedBonus() {
        // given
        PublisherRewardId publisherRewardId = createPublisherRewardId();

        when(countryService.findCurrencyBy(MERCHANT_COUNTRY_CODE_ID))
                .thenReturn(MERCHANT_CURRENCY_IDR);
        when(countryService.findCurrencyBy(PUBLISHER_COUNTRY_CODE_MY))
                .thenReturn(PUBLISHER_CURRENCY_MYR);

        // when
        PublisherRewardDetails actual = underTest.createPublisherRewardDetailsFrom(
                CAMPAIGN_CLOSURE_ID, publisherRewardId, BigDecimal.ZERO, BigDecimal.ZERO,
                CLICK_COUNT1, IMPRESSION_COUNT1,
                DEFAULT_COUNT_VALUE, BigDecimal.ZERO, BigDecimal.ZERO, FIXED_BONUS1,
                FIXED_BONUS_IN_USD1);

        // then
        assertPublisherRewardDetails(actual, CAMPAIGN_CLOSURE_ID, PUBLISHER_ID1,
                PUBLISHER_CURRENCY_MYR,
                CAMPAIGN_ID1, MERCHANT_CURRENCY_IDR, BigDecimal.ZERO, BigDecimal.ZERO,
                CLICK_COUNT1, IMPRESSION_COUNT1, DEFAULT_COUNT_VALUE, BigDecimal.ZERO,
                BigDecimal.ZERO, FIXED_BONUS1, FIXED_BONUS_IN_USD1);
    }

    @Test
    public void testMergeConversionRewardAndCreativeRewardShouldReturnCorrectDataWhenCalled() {
        // given
        PublisherRewardDetails conversionReward = createConversionReward();
        PublisherRewardDetails creativeReward = createCreativeReward1();

        // when
        PublisherRewardDetails actual = underTest.mergeConversionRewardAndCreativeReward(
                CAMPAIGN_CLOSURE_ID, conversionReward, creativeReward);

        // then
        assertPublisherRewardDetails(actual, CAMPAIGN_CLOSURE_ID, PUBLISHER_ID1,
                PUBLISHER_CURRENCY_MYR,
                CAMPAIGN_ID1, MERCHANT_CURRENCY_IDR, AGGREGATED_REWARD,
                AGGREGATED_REWARD_IN_USD, CLICK_COUNT1, IMPRESSION_COUNT1, SALES_COUNT,
                CONVERSION_BONUS1, CONVERSION_BONUS_IN_USD1, BigDecimal.ZERO,
                BigDecimal.ZERO);
    }

    @Test
    public void testMergePublisherRewardShouldReturnCorrectDataWhenCalled() {
        // given
        PublisherRewardDetails publisherReward = createPublisherReward();
        PublisherRewardDetails fixedBobus = createFixedBonus3();

        // when
        PublisherRewardDetails actual = underTest
                .mergePublisherReward(CAMPAIGN_CLOSURE_ID, publisherReward,
                        fixedBobus);
        BigDecimal amount = new BigDecimal("220.21");
        BigDecimal amountInUsd = new BigDecimal("23.66");

        // then
        assertPublisherRewardDetails(actual, CAMPAIGN_CLOSURE_ID, PUBLISHER_ID3,
                PUBLISHER_CURRENCY_MYR, CAMPAIGN_ID3, MERCHANT_CURRENCY_IDR, amount,
                amountInUsd, DEFAULT_COUNT_VALUE, DEFAULT_COUNT_VALUE,
                DEFAULT_COUNT_VALUE, CONVERSION_BONUS2, CONVERSION_BONUS_IN_USD2,
                FIXED_BONUS2, FIXED_BONUS_IN_USD2);
    }

    @Test
    public void testCalculateRewardInUsdShouldReturnCorrectRewardWhenAllRewardsAreNotNull() {
        // given
        BigDecimal expected = new BigDecimal("1180.245");

        // when
        BigDecimal actual = underTest.calculateRewardInUsd(CLICK_REWARD_IN_USD1,
                CLICK_REWARD_IN_USD2);

        // then
        assertEquals(expected, actual);
    }

    @Test
    public void testCalculateRewardInUsdShouldReturnCorrectRewardWhenOneRewardIsNull() {
        // given
        BigDecimal expected = new BigDecimal("323.456");

        // when
        BigDecimal actual = underTest.calculateRewardInUsd(CLICK_REWARD_IN_USD1, null);

        // then
        assertEquals(expected, actual);
    }

    @Test
    public void testCalculateRewardInUsdShouldReturnNullWhenAllRewardsIsNull() {

        // when
        BigDecimal actual = underTest.calculateRewardInUsd(null, null);

        // then
        assertNull(actual);
    }

    private PublisherRewardId createPublisherRewardId() {
        return new PublisherRewardId(CAMPAIGN_CLOSURE_ID, CAMPAIGN_ID1,
                MERCHANT_COUNTRY_CODE_ID, PUBLISHER_ID1, PUBLISHER_COUNTRY_CODE_MY);
    }

    private PublisherRewardId createPublisherRewardId2() {
        return new PublisherRewardId(CAMPAIGN_CLOSURE_ID, CAMPAIGN_ID2,
                MERCHANT_COUNTRY_CODE_ID, PUBLISHER_ID2, PUBLISHER_COUNTRY_CODE_SG);
    }

    private PublisherRewardId createPublisherRewardId3() {
        return new PublisherRewardId(CAMPAIGN_CLOSURE_ID, CAMPAIGN_ID3,
                MERCHANT_COUNTRY_CODE_ID, PUBLISHER_ID3, PUBLISHER_COUNTRY_CODE_SG);
    }

    private PublisherRewardDetails createConversionReward() {
        return new PublisherRewardDetails(CAMPAIGN_CLOSURE_ID, PUBLISHER_ID1,
                PUBLISHER_CURRENCY_MYR, CAMPAIGN_ID1, MERCHANT_CURRENCY_IDR,
                CONVERSION_REWARD, CONVERSION_REWARD_IN_USD, DEFAULT_COUNT_VALUE,
                DEFAULT_COUNT_VALUE, SALES_COUNT, CONVERSION_BONUS1,
                CONVERSION_BONUS_IN_USD1, BigDecimal.ZERO, BigDecimal.ZERO);
    }

    private PublisherRewardDetails createCreativeReward1() {
        return new PublisherRewardDetails(CAMPAIGN_CLOSURE_ID, PUBLISHER_ID1,
                PUBLISHER_CURRENCY_MYR, CAMPAIGN_ID1, MERCHANT_CURRENCY_IDR,
                CLICK_REWARD1, CLICK_REWARD_IN_USD1, CLICK_COUNT1, IMPRESSION_COUNT1,
                DEFAULT_COUNT_VALUE, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO,
                BigDecimal.ZERO);
    }

    private PublisherRewardDetails createCreativeReward2() {
        return new PublisherRewardDetails(CAMPAIGN_CLOSURE_ID, PUBLISHER_ID2,
                PUBLISHER_CURRENCY_SGD, CAMPAIGN_ID2, MERCHANT_CURRENCY_IDR,
                CLICK_REWARD2, CLICK_REWARD_IN_USD2, CLICK_COUNT2, IMPRESSION_COUNT2,
                DEFAULT_COUNT_VALUE, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO,
                BigDecimal.ZERO);
    }

    private PublisherRewardDetails createFixedBonus() {
        return new PublisherRewardDetails(CAMPAIGN_CLOSURE_ID, PUBLISHER_ID1,
                PUBLISHER_CURRENCY_MYR, CAMPAIGN_ID1, MERCHANT_CURRENCY_IDR,
                BigDecimal.ZERO, BigDecimal.ZERO, DEFAULT_COUNT_VALUE,
                DEFAULT_COUNT_VALUE, DEFAULT_COUNT_VALUE, BigDecimal.ZERO,
                BigDecimal.ZERO, FIXED_BONUS1, FIXED_BONUS_IN_USD1);
    }

    private PublisherRewardDetails createFixedBonus3() {
        return new PublisherRewardDetails(CAMPAIGN_CLOSURE_ID, PUBLISHER_ID3,
                PUBLISHER_CURRENCY_MYR, CAMPAIGN_ID3, MERCHANT_CURRENCY_IDR,
                BigDecimal.ZERO, BigDecimal.ZERO, DEFAULT_COUNT_VALUE,
                DEFAULT_COUNT_VALUE, DEFAULT_COUNT_VALUE, BigDecimal.ZERO,
                BigDecimal.ZERO, FIXED_BONUS2, FIXED_BONUS_IN_USD2);
    }

    private PublisherRewardDetails createPublisherReward() {
        return new PublisherRewardDetails(CAMPAIGN_CLOSURE_ID, PUBLISHER_ID3,
                PUBLISHER_CURRENCY_MYR, CAMPAIGN_ID3, MERCHANT_CURRENCY_IDR, REWARD,
                REWARD_IN_USD, DEFAULT_COUNT_VALUE, DEFAULT_COUNT_VALUE,
                DEFAULT_COUNT_VALUE,
                CONVERSION_BONUS2, CONVERSION_BONUS_IN_USD2, BigDecimal.ZERO,
                BigDecimal.ZERO);
    }

    private void assertPublisherRewardDetails(PublisherRewardDetails actual,
            Long campaignClosureId, Long publisherId, String publisherCountryCurrency,
            Long campaignId,
            String merchantCountryCurrency, BigDecimal reward, BigDecimal rewardInUsd,
            Long clickCount, Long impressionCount, Long salesCount,
            BigDecimal conversionBonus, BigDecimal conversionBonusInUsd,
            BigDecimal fixedBonus, BigDecimal fixedBonusInUsd) {
        assertNotNull(actual);
        assertEquals(campaignClosureId, actual.getCampaignClosureId());
        assertEquals(publisherId, actual.getPublisherId());
        assertEquals(publisherCountryCurrency, actual.getPublisherCountryCurrency());
        assertEquals(campaignId, actual.getCampaignId());
        assertEquals(merchantCountryCurrency, actual.getMerchantCountryCurrency());
        assertEquals(reward, actual.getReward());
        assertEquals(rewardInUsd, actual.getRewardInUsd());
        assertEquals(clickCount, actual.getClickCount());
        assertEquals(impressionCount, actual.getImpressionCount());
        assertEquals(salesCount, actual.getSalesCount());
        assertEquals(conversionBonus, actual.getConversionBonus());
        assertEquals(conversionBonusInUsd, actual.getConversionBonusInUsd());
        assertEquals(fixedBonus, actual.getFixedBonus());
        assertEquals(fixedBonusInUsd, actual.getFixedBonusInUsd());
    }
}
