/**
 * Copyright © 2021 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.model;

import java.math.BigDecimal;
import java.time.YearMonth;
import java.time.ZonedDateTime;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * DTO for holding the data of account payment history.
 *
 * <AUTHOR>
 */
@AllArgsConstructor @Getter
public class IntegrationTestAccountPaymentHistory {

    private final Long campaignClosureId;
    private final Long publisherId;
    private final YearMonth rewardMonth;
    private final BigDecimal reward;
    private final BigDecimal rewardInUsd;
    private final PaymentStatus paymentStatus;
    private final String createdBy;
    private final ZonedDateTime createdOn;
}
