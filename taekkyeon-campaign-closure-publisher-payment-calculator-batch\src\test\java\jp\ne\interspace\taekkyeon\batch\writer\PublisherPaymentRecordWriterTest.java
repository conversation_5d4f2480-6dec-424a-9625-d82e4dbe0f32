/**
 * Copyright © 2021 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.batch.writer;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

import org.easybatch.core.record.Batch;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;

import jp.ne.interspace.taekkyeon.junit.TaekkyeonMockitoJunitRunner;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonModules;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonPropertiesJunitModule;
import jp.ne.interspace.taekkyeon.model.PaymentGenerationStatus;
import jp.ne.interspace.taekkyeon.model.PublisherPaymentPayload;
import jp.ne.interspace.taekkyeon.model.PublisherPaymentRecord;
import jp.ne.interspace.taekkyeon.model.PublisherRewardDetails;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.CampaignClosureMapper;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.PublisherAccountCampaignRewardHistoryMapper;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.PublisherAccountPaymentHistoryMapper;
import jp.ne.interspace.taekkyeon.service.PublisherRewardService;
import jp.ne.interspace.taekkyeon.validator.DatabaseOperationValidator;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
/**
 * Unit test for {@link PublisherPaymentRecordWriter}.
 *
 * <AUTHOR>
 */
@RunWith(TaekkyeonMockitoJunitRunner.class)
@TaekkyeonModules(TaekkyeonPropertiesJunitModule.class)
public class PublisherPaymentRecordWriterTest {

    private static final String TARGET_MONTH = "targetMonth";

    private static final long PUBLISHER_ID = 1;
    private static final long CAMPAIGN_ID = 2;
    private static final long CAMPAIGN_CLOSURE_ID = 2;

    private static final BigDecimal REWARD = BigDecimal.ONE;
    private static final BigDecimal REWARD_IN_USD = BigDecimal.TEN;
    private static final long CLICK_COUNT = 3;
    private static final long IMPRESSION_COUNT = 4;
    private static final long SALES_COUNT = 5;

    private static final BigDecimal CONVERSION_BONUS = new BigDecimal("20.00");
    private static final BigDecimal CONVERSION_BONUS_IN_USD = new BigDecimal("0.60");
    private static final BigDecimal FIXED_BONUS = new BigDecimal("10.00");
    private static final BigDecimal FIXED_BONUS_IN_USD = new BigDecimal("0.1");

    private static final String MERCHANT_COUNTRY_CURRENCY_IDR = "IDR";
    private static final String PUBLISHER_COUNTRY_CURRENCY_IDR = "IDR";
    private static final String PUBLISHER_COUNTRY_CURRENCY_SGD = "SGD";

    @InjectMocks @Spy
    private PublisherPaymentRecordWriter underTest;

    @Mock
    private PublisherAccountCampaignRewardHistoryMapper campaignRewardHistoryMapper;

    @Mock
    private PublisherAccountPaymentHistoryMapper paymentHistoryMapper;

    @Mock
    private DatabaseOperationValidator databaseOperationValidator;

    @Mock
    private PublisherRewardService publisherRewardService;

    @Mock
    private CampaignClosureMapper campaignClosureMapper;

    @Test
    public void testWriteRecordsShouldUpdatePaymentAndCampaignRewardHistoryOfTheGivenPublishersWhenMerchantCountryCurrencyIsNotEqualToPublisherCountryCurrency()
            throws Exception {
        // given
        when(publisherRewardService.convertByCurrencyCode(REWARD,
                MERCHANT_COUNTRY_CURRENCY_IDR, PUBLISHER_COUNTRY_CURRENCY_SGD,
                TARGET_MONTH)).thenReturn(REWARD);
        List<PublisherRewardDetails> campaignRewards = Arrays
                .asList(new PublisherRewardDetails(CAMPAIGN_CLOSURE_ID, PUBLISHER_ID,
                        PUBLISHER_COUNTRY_CURRENCY_SGD, CAMPAIGN_ID,
                        MERCHANT_COUNTRY_CURRENCY_IDR, REWARD, REWARD_IN_USD, CLICK_COUNT,
                        IMPRESSION_COUNT, SALES_COUNT, CONVERSION_BONUS,
                        CONVERSION_BONUS_IN_USD, FIXED_BONUS, FIXED_BONUS_IN_USD));

        PublisherPaymentRecord record = new PublisherPaymentRecord(null,
                new PublisherPaymentPayload(TARGET_MONTH, campaignRewards,
                        CAMPAIGN_CLOSURE_ID));

        Batch batch = new Batch(record);

        int insertedItemCount = 1;
        when(campaignRewardHistoryMapper.insertHistoryItemBy(CAMPAIGN_CLOSURE_ID,
                TARGET_MONTH, PUBLISHER_ID, CAMPAIGN_ID, REWARD, REWARD_IN_USD,
                CLICK_COUNT, IMPRESSION_COUNT, SALES_COUNT, CONVERSION_BONUS,
                CONVERSION_BONUS_IN_USD, FIXED_BONUS, FIXED_BONUS_IN_USD))
                        .thenReturn(insertedItemCount);
        when(paymentHistoryMapper.insertHistoryItemBy(CAMPAIGN_CLOSURE_ID, PUBLISHER_ID,
                TARGET_MONTH)).thenReturn(insertedItemCount);
        doNothing().when(underTest).updateIsPaymentGenerated(CAMPAIGN_CLOSURE_ID);

        // when
        underTest.writeRecords(batch);

        // then
        verify(campaignRewardHistoryMapper).deleteBy(PUBLISHER_ID, CAMPAIGN_CLOSURE_ID);
        verify(paymentHistoryMapper).deleteBy(PUBLISHER_ID, CAMPAIGN_CLOSURE_ID);
        verify(underTest).updateIsPaymentGenerated(CAMPAIGN_CLOSURE_ID);
        verify(databaseOperationValidator)
                .validateInsertedRowCount(insertedItemCount);
    }

    @Test
    public void testWriteRecordsShouldUpdatePaymentAndCampaignRewardHistoryOfTheGivenPublishersWhenMerchantCountryCurrencyIsEqualToPublisherCountryCurrency()
            throws Exception {
        // given
        when(publisherRewardService.convertByCurrencyCode(REWARD,
                MERCHANT_COUNTRY_CURRENCY_IDR, PUBLISHER_COUNTRY_CURRENCY_IDR,
                TARGET_MONTH)).thenReturn(REWARD);
        List<PublisherRewardDetails> campaignRewards = Arrays
                .asList(new PublisherRewardDetails(CAMPAIGN_CLOSURE_ID, PUBLISHER_ID,
                        PUBLISHER_COUNTRY_CURRENCY_IDR, CAMPAIGN_ID,
                        MERCHANT_COUNTRY_CURRENCY_IDR, REWARD, REWARD_IN_USD, CLICK_COUNT,
                        IMPRESSION_COUNT, SALES_COUNT, CONVERSION_BONUS,
                        CONVERSION_BONUS_IN_USD, FIXED_BONUS, FIXED_BONUS_IN_USD));

        PublisherPaymentRecord record = new PublisherPaymentRecord(null,
                new PublisherPaymentPayload(TARGET_MONTH, campaignRewards,
                        CAMPAIGN_CLOSURE_ID));

        Batch batch = new Batch(record);

        int insertedItemCount = 1;
        when(campaignRewardHistoryMapper.insertHistoryItemBy(CAMPAIGN_CLOSURE_ID,
                TARGET_MONTH, PUBLISHER_ID, CAMPAIGN_ID, REWARD, REWARD_IN_USD,
                CLICK_COUNT, IMPRESSION_COUNT, SALES_COUNT, CONVERSION_BONUS,
                CONVERSION_BONUS_IN_USD, FIXED_BONUS, FIXED_BONUS_IN_USD))
                        .thenReturn(insertedItemCount);
        when(paymentHistoryMapper.insertHistoryItemBy(CAMPAIGN_CLOSURE_ID, PUBLISHER_ID,
                TARGET_MONTH)).thenReturn(insertedItemCount);
        doNothing().when(underTest).updateIsPaymentGenerated(CAMPAIGN_CLOSURE_ID);

        // when
        underTest.writeRecords(batch);

        // then
        verify(campaignRewardHistoryMapper).deleteBy(PUBLISHER_ID, CAMPAIGN_CLOSURE_ID);
        verify(paymentHistoryMapper).deleteBy(PUBLISHER_ID, CAMPAIGN_CLOSURE_ID);
        verify(underTest).updateIsPaymentGenerated(CAMPAIGN_CLOSURE_ID);
        verify(databaseOperationValidator)
                .validateInsertedRowCount(insertedItemCount);
    }

    @Test
    public void testUpdateIsPaymentGeneratedShoulDoNothingWhenGivenIdEqualsZero() {
        // when
        underTest.updateIsPaymentGenerated(0);

        // then
        verify(campaignClosureMapper, never()).updateIsPaymentGenerated(anyLong(),
                any(PaymentGenerationStatus.class));
    }

    @Test
    public void testUpdateIsPaymentGeneratedShoulCallUpdateFunctionWhenGivenIdsSetIsNotZero() {
        // when
        underTest.updateIsPaymentGenerated(CAMPAIGN_CLOSURE_ID);

        // then
        verify(campaignClosureMapper).updateIsPaymentGenerated(CAMPAIGN_CLOSURE_ID,
                PaymentGenerationStatus.IS_PROCESSING);
    }
}
