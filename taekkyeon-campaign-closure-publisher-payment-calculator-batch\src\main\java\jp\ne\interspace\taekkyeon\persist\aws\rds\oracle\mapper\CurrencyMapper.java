/**
 * Copyright © 2021 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper;

import org.apache.ibatis.annotations.Select;

import jp.ne.interspace.taekkyeon.multiline.Multiline;

/**
 * MyBatis mapper for getting the currency data.
 *
 * <AUTHOR>
 */
public interface CurrencyMapper {

    /**
        SELECT
          fractional_digits
        FROM
          currency_master
        WHERE
          currency = #{currency}
     */
    @Multiline String SELECT_FRACTIONAL_DIGITS = "";

    /**
     * Returns the fractional digits of the given {@code currency}.
     *
     * @param currency
     *          the given currency
     * @return the exchange rate of the given {@code currency}
     */
    @Select(SELECT_FRACTIONAL_DIGITS)
    int findFractionalDigitsBy(String currency);
}
