/**
 * Copyright © 2021 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper;

import com.google.inject.Inject;

import org.junit.Test;
import org.junit.runner.RunWith;

import jp.ne.interspace.taekkyeon.junit.TaekkyeonHsqldbJunitRunner;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonHsqldbOracleJunitModule;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonModules;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonPropertiesJunitModule;

import static org.junit.Assert.assertEquals;

/**
 * Integration test for {@link PublisherAccountPaymentHistoryMapper}.
 *
 * <AUTHOR>
 */
@RunWith(TaekkyeonHsqldbJunitRunner.class)
@TaekkyeonModules({ TaekkyeonPropertiesJunitModule.class,
        TaekkyeonHsqldbOracleJunitModule.class })
public class PublisherAccountPaymentHistoryMapperTest {

    @Inject
    private PublisherAccountPaymentHistoryMapper underTest;

    @Test
    public void testDeleteByShouldReturnOneWhenCalled() {
        // given
        Long campaignClosureId = 1L;
        Long publisherId = 1L;

        // when
        int actual = underTest.deleteBy(publisherId, campaignClosureId);

        // then
        assertEquals(1, actual);
    }

    @Test
    public void testInsertHistoryItemByShouldReturnOneWhenCalled() {
        // given
        Long publisherId = 10L;
        String targetMonth = "201711";
        Long campaignClosureId = 2L;

        // when
        int actual = underTest.insertHistoryItemBy(publisherId, campaignClosureId,
                targetMonth);

        // then
        assertEquals(1, actual);
    }
}
