/**
 * Copyright © 2021 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import javax.inject.Singleton;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.inject.Inject;

import jp.ne.interspace.taekkyeon.model.CurrencyExchangeRateCacheKey;
import jp.ne.interspace.taekkyeon.model.PublisherFixedBonusDetails;
import jp.ne.interspace.taekkyeon.model.PublisherRewardConversionDetails;
import jp.ne.interspace.taekkyeon.model.PublisherRewardCreativeAccessDetails;
import jp.ne.interspace.taekkyeon.model.PublisherRewardDetails;
import jp.ne.interspace.taekkyeon.model.PublisherRewardId;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.BonusMapper;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.ConversionMapper;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.CreativeAccessLogSummaryMapper;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.CurrencyExchangeRateMapper;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.CurrencyMapper;

import static java.time.ZoneOffset.UTC;
import static java.util.concurrent.TimeUnit.MINUTES;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.DATE_TIME_FORMATTER_YYYYMM;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.INTERMEDIARY_CURRENCY;

/**
 * Service for publisher rewards.
 *
 * <AUTHOR>
 */
@Singleton
public class PublisherRewardService {

    private static final int FIRST_DAY_OF_MONTH = 1;
    private static final long DEFAULT_COUNT_VALUE = 0;
    private static final BigDecimal DEFAULT_REWARD_VALUE = BigDecimal.ZERO;

    private LoadingCache<CurrencyExchangeRateCacheKey, BigDecimal>
            currencyExchangeRateCache = CacheBuilder.newBuilder()
            .maximumSize(1000).expireAfterWrite(5, MINUTES)
            .build(new CacheLoader<CurrencyExchangeRateCacheKey, BigDecimal>() {

                @Override
                public BigDecimal load(CurrencyExchangeRateCacheKey cacheKey)
                        throws Exception {
                    return currencyExchangeRateMapper.findExchangeRateBy(cacheKey);
                }
            });

    private LoadingCache<String, Integer> currencyFractionalDigitsCache = CacheBuilder
            .newBuilder().maximumSize(20).expireAfterWrite(5, MINUTES)
            .build(new CacheLoader<String, Integer>() {

                @Override
                public Integer load(String currency) throws Exception {
                    return currencyMapper.findFractionalDigitsBy(currency);
                }
            });

    @Inject
    private CurrencyExchangeRateMapper currencyExchangeRateMapper;

    @Inject
    private CurrencyMapper currencyMapper;

    @Inject
    private CountryService countryService;

    @Inject
    private ConversionMapper conversionMapper;

    @Inject
    private CreativeAccessLogSummaryMapper creativeAccessLogSummaryMapper;

    @Inject
    private BonusMapper bonusMapper;

    /**
     * Returns the {@link PublisherRewardDetails} for each publisher in the given month.
     *
     * @param campaignClosureId
     *            the given campaign closure id
     * @param campaignId
     *            the given campaignId
     * @param closedFrom
     *            the given closed from
     * @param closedTo
     *            the given closed to
     * @return the {@link PublisherRewardDetails} each publisher in the given period time
     */
    public List<PublisherRewardDetails> findPublisherRewardDetailsBy(
            long campaignClosureId, long campaignId, LocalDateTime closedFrom,
            LocalDateTime closedTo) {
        List<PublisherRewardConversionDetails> rewardConversionDetails = conversionMapper
                .findPublisherRewardsBy(closedFrom, closedTo, campaignId,
                        campaignClosureId);
        List<PublisherRewardCreativeAccessDetails> rewardCreativeAccessDetails =
                creativeAccessLogSummaryMapper.findPublisherRewardsBy(closedFrom,
                        closedTo, campaignId, campaignClosureId);
        List<PublisherFixedBonusDetails> publisherFixedBonusDetails = bonusMapper
                .findPublisherBonusesBy(closedFrom, closedTo, campaignId,
                        campaignClosureId);
        return createPublisherRewardsFrom(campaignClosureId, rewardConversionDetails,
                rewardCreativeAccessDetails, publisherFixedBonusDetails);
    }

    /**
     * Returns the converted amount in target currency if source currency and target
     * currency are different, otherwise return the same amount.
     *
     * @param amountInSourceCurrency
     *            the given amount in source currency
     * @param sourceCurrency
     *            the given source currency
     * @param targetCurrency
     *            the given target currency
     * @param targetMonth
     *            the given target month
     * @return the converted amount in target currency if source currency and target
     *         currency are different, otherwise return the same amount
     */
    public BigDecimal convertByCurrencyCode(BigDecimal amountInSourceCurrency,
            String sourceCurrency, String targetCurrency, String targetMonth) {
        if (sourceCurrency.equals(targetCurrency)) {
            return amountInSourceCurrency;
        }
        YearMonth yearMonth = createYearMonthFrom(targetMonth);
        BigDecimal intermediaryCurrencyToSourceCurrencyRate = currencyExchangeRateCache
                .getUnchecked(new CurrencyExchangeRateCacheKey(INTERMEDIARY_CURRENCY,
                        sourceCurrency, yearMonth));
        BigDecimal intermediaryCurrencyToTargetCurrencyRate = currencyExchangeRateCache
                .getUnchecked(new CurrencyExchangeRateCacheKey(INTERMEDIARY_CURRENCY,
                        targetCurrency, yearMonth));
        int fractionalDigits = currencyFractionalDigitsCache.getUnchecked(targetCurrency);
        return amountInSourceCurrency.multiply(intermediaryCurrencyToTargetCurrencyRate)
                .divide(intermediaryCurrencyToSourceCurrencyRate, fractionalDigits,
                        RoundingMode.HALF_UP);
    }

    /**
     * Returns the sum of rewards in usd if not null, else return null.
     *
     * @param firstRewardInUsd
     *          the given first reward in usd
     * @param secondRewardInUsd
     *          the given second reward in usd
     * @return the sum of rewards in usd if not null, else return null
     */
    public BigDecimal calculateRewardInUsd(BigDecimal firstRewardInUsd,
            BigDecimal secondRewardInUsd) {
        if (firstRewardInUsd == null) {
            return secondRewardInUsd;
        }
        if (secondRewardInUsd == null) {
            return firstRewardInUsd;
        }
        return firstRewardInUsd.add(secondRewardInUsd);
    }

    @VisibleForTesting
    ZonedDateTime createDateTimeFrom(String targetMonth, ZoneId zoneId, int numberOfMonths) {
        YearMonth yearMonth = createYearMonthFrom(targetMonth).plusMonths(numberOfMonths);
        return ZonedDateTime.ofInstant(
                yearMonth.atDay(FIRST_DAY_OF_MONTH).atStartOfDay(zoneId).toInstant(),
                UTC);
    }

    @VisibleForTesting
    List<PublisherRewardDetails> createPublisherRewardsFrom(long campaignClosureId,
            List<PublisherRewardConversionDetails> rewardConversionDetails,
            List<PublisherRewardCreativeAccessDetails> rewardCreativeAccessDetails,
            List<PublisherFixedBonusDetails> rewardFixedBonusDetails) {
        Map<PublisherRewardId, PublisherRewardDetails> conversionRewardCache =
                createConversionRewardCacheFrom(campaignClosureId,
                        rewardConversionDetails);

        Map<PublisherRewardId, PublisherRewardDetails> creativeRewardCache =
                createCreativeRewardCacheFrom(campaignClosureId,
                        rewardCreativeAccessDetails);

        Map<PublisherRewardId, PublisherRewardDetails> bonusCache =
                createBonusRewardCacheFrom(campaignClosureId, rewardFixedBonusDetails);

        Map<PublisherRewardId, PublisherRewardDetails> rewardCache = Stream
                .of(conversionRewardCache, creativeRewardCache)
                .flatMap(rewards -> rewards.entrySet().stream())
                .collect(Collectors.toMap(Entry::getKey, Entry::getValue,
                        (conversionReward,
                                creativeReward) -> mergeConversionRewardAndCreativeReward(
                                        campaignClosureId, conversionReward,
                                        creativeReward),
                        LinkedHashMap::new));

        Map<PublisherRewardId, PublisherRewardDetails> resultCache = Stream
                .of(rewardCache, bonusCache)
                .flatMap(rewards -> rewards.entrySet().stream())
                .collect(Collectors.toMap(Entry::getKey, Entry::getValue,
                        (reward, bonusReward) -> mergePublisherReward(campaignClosureId,
                                reward, bonusReward),
                        LinkedHashMap::new));

        return resultCache.values().stream()
                .filter(result -> result.getReward().doubleValue() > 0)
                .collect(Collectors.toList());
    }

    @VisibleForTesting
    Map<PublisherRewardId, PublisherRewardDetails> createConversionRewardCacheFrom(
            long campaignClosureId,
            List<PublisherRewardConversionDetails> rewardConversionDetails) {
        return rewardConversionDetails.stream().collect(Collectors.toMap(
                reward -> reward.getPublisherRewardId(),
                reward -> createPublisherRewardDetailsFrom(campaignClosureId,
                        reward.getPublisherRewardId(), reward.getReward(),
                        reward.getRewardInUsd(), DEFAULT_COUNT_VALUE, DEFAULT_COUNT_VALUE,
                        reward.getSalesCount(), reward.getConversionBonus(),
                        reward.getConversionBonusInUsd(), DEFAULT_REWARD_VALUE,
                        DEFAULT_REWARD_VALUE)));
    }

    @VisibleForTesting
    Map<PublisherRewardId, PublisherRewardDetails> createCreativeRewardCacheFrom(
            long campaignClosureId,
            List<PublisherRewardCreativeAccessDetails> rewardCreativeAccessDetails) {
        return rewardCreativeAccessDetails.stream()
                .collect(Collectors.toMap(reward -> reward.getPublisherRewardId(),
                        reward -> createPublisherRewardDetailsFrom(campaignClosureId,
                                reward.getPublisherRewardId(), reward.getClickReward(),
                                reward.getClickRewardInUsd(), reward.getClickCount(),
                                reward.getImpressionCount(), DEFAULT_COUNT_VALUE,
                                DEFAULT_REWARD_VALUE, DEFAULT_REWARD_VALUE,
                                DEFAULT_REWARD_VALUE, DEFAULT_REWARD_VALUE)));
    }

    @VisibleForTesting
    Map<PublisherRewardId, PublisherRewardDetails> createBonusRewardCacheFrom(
            long campaignClosureId,
            List<PublisherFixedBonusDetails> rewardFixedBonusDetails) {
        return rewardFixedBonusDetails.stream().collect(Collectors.toMap(
                reward -> reward.getPublisherRewardId(),
                reward -> createPublisherRewardDetailsFrom(campaignClosureId,
                        reward.getPublisherRewardId(), DEFAULT_REWARD_VALUE,
                        DEFAULT_REWARD_VALUE, DEFAULT_COUNT_VALUE, DEFAULT_COUNT_VALUE,
                        DEFAULT_COUNT_VALUE, DEFAULT_REWARD_VALUE, DEFAULT_REWARD_VALUE,
                        reward.getFixedBonus(), reward.getFixedBonusInUsd())));
    }

    @VisibleForTesting
    PublisherRewardDetails createPublisherRewardDetailsFrom(long campaignClosureId,
            PublisherRewardId publisherRewardId, BigDecimal reward,
            BigDecimal rewardInUsd, Long clickCount, Long impressionCount,
            Long salesCount, BigDecimal conversionBonus, BigDecimal conversionBonusInUsd,
            BigDecimal fixedBonus, BigDecimal fixedBonusInUsd) {
        String publisherCountryCurrency = countryService
                .findCurrencyBy(publisherRewardId.getPublisherCountryCode());
        String merchantCountryCurrency = countryService
                .findCurrencyBy(publisherRewardId.getMerchantCountryCode());

        return new PublisherRewardDetails(campaignClosureId,
                publisherRewardId.getPublisherId(), publisherCountryCurrency,
                publisherRewardId.getCampaignId(), merchantCountryCurrency, reward,
                rewardInUsd, clickCount, impressionCount, salesCount, conversionBonus,
                conversionBonusInUsd, fixedBonus, fixedBonusInUsd);
    }

    @VisibleForTesting
    PublisherRewardDetails mergeConversionRewardAndCreativeReward(long campaignClosureId,
            PublisherRewardDetails conversionReward,
            PublisherRewardDetails creativeReward) {
        return new PublisherRewardDetails(campaignClosureId,
                conversionReward.getPublisherId(),
                conversionReward.getPublisherCountryCurrency(),
                conversionReward.getCampaignId(),
                conversionReward.getMerchantCountryCurrency(),
                conversionReward.getReward().add(creativeReward.getReward()),
                calculateRewardInUsd(conversionReward.getRewardInUsd(),
                        creativeReward.getRewardInUsd()),
                creativeReward.getClickCount(), creativeReward.getImpressionCount(),
                conversionReward.getSalesCount(), conversionReward.getConversionBonus(),
                conversionReward.getConversionBonusInUsd(), DEFAULT_REWARD_VALUE,
                DEFAULT_REWARD_VALUE);
    }

    @VisibleForTesting
    PublisherRewardDetails mergePublisherReward(long campaignClosureId,
            PublisherRewardDetails reward, PublisherRewardDetails bonusReward) {
        return new PublisherRewardDetails(campaignClosureId, reward.getPublisherId(),
                reward.getPublisherCountryCurrency(), reward.getCampaignId(),
                reward.getMerchantCountryCurrency(),
                reward.getReward().add(bonusReward.getFixedBonus()),
                reward.getRewardInUsd().add(bonusReward.getFixedBonusInUsd()),
                reward.getClickCount(), reward.getImpressionCount(),
                reward.getSalesCount(), reward.getConversionBonus(),
                reward.getConversionBonusInUsd(), bonusReward.getFixedBonus(),
                bonusReward.getFixedBonusInUsd());
    }

    @VisibleForTesting
    ZoneId findZoneIdBy(String countryCode) {
        return ZoneId.of(countryService.findZoneIdBy(countryCode));
    }

    private YearMonth createYearMonthFrom(String targetMonth) {
        return YearMonth.parse(targetMonth, DATE_TIME_FORMATTER_YYYYMM);
    }
}
