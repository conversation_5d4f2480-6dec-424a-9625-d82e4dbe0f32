/**
 * Copyright © 2021 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

import com.google.inject.Inject;

import org.junit.Test;
import org.junit.runner.RunWith;

import jp.ne.interspace.taekkyeon.junit.TaekkyeonHsqldbJunitRunner;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonHsqldbOracleJunitModule;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonModules;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonPropertiesJunitModule;
import jp.ne.interspace.taekkyeon.model.PublisherRewardCreativeAccessDetails;
import jp.ne.interspace.taekkyeon.model.PublisherRewardId;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertSame;

/**
 * Unit test for {@link CreativeAccessLogSummaryMapper}.
 *
 * <AUTHOR>
 */
@RunWith(TaekkyeonHsqldbJunitRunner.class)
@TaekkyeonModules({ TaekkyeonPropertiesJunitModule.class,
        TaekkyeonHsqldbOracleJunitModule.class })
public class CreativeAccessLogSummaryMapperTest {

    private static final LocalDateTime CLOSED_FROM = LocalDateTime.of(2020, 8, 15, 00, 00);
    private static final LocalDateTime CLOSED_TO = LocalDateTime.of(2020, 8, 25, 00, 00);
    private static final long CAMPAIGN_ID = 1;
    private static final long CAMPAIGN_CLOSURE_ID = 1;

    @Inject
    private CreativeAccessLogSummaryMapper underTest;

    @Test
    public void testFindPublisherRewardsByShouldReturnCorrectDataWhenCalled() {
        // when
        List<PublisherRewardCreativeAccessDetails> actual = underTest
                .findPublisherRewardsBy(CLOSED_FROM, CLOSED_TO, CAMPAIGN_ID,
                        CAMPAIGN_CLOSURE_ID);

        // then
        assertEquals(3, actual.size());
        assertFields(actual.get(0), 1L, 1L, "ID", "ID", 1L, 1L, new BigDecimal("100.00"),
                new BigDecimal("200.00"));
        assertFields(actual.get(1), 2L, 1L, "ID", "ID", 2L, 2L, new BigDecimal("300.00"),
                new BigDecimal("50.00"));
        assertFields(actual.get(2), 3L, 1L, "ID", "ID", 4L, 4L, new BigDecimal("300.00"),
                new BigDecimal("50.00"));
    }

    private void assertFields(PublisherRewardCreativeAccessDetails creative,
            Long expectedPublisherId, Long expectedCampaignId,
            String expectedMerchantCountryCode, String expectedPublisherCountryCode,
            Long expectedClickCount, Long expectedImpressionCount,
            BigDecimal expectedClickReward, BigDecimal expectedClickRewardInUsd) {
        PublisherRewardId publisherRewardId = creative.getPublisherRewardId();
        assertNotNull(publisherRewardId);
        assertSame(expectedCampaignId, publisherRewardId.getCampaignId());
        assertSame(expectedPublisherId, publisherRewardId.getPublisherId());
        assertEquals(expectedMerchantCountryCode,
                publisherRewardId.getMerchantCountryCode());
        assertEquals(expectedPublisherCountryCode,
                publisherRewardId.getPublisherCountryCode());
        assertSame(expectedClickCount, creative.getClickCount());
        assertSame(expectedImpressionCount, creative.getImpressionCount());
        assertEquals(expectedClickReward, creative.getClickReward());
        assertEquals(expectedClickRewardInUsd, creative.getClickRewardInUsd());
    }
}
