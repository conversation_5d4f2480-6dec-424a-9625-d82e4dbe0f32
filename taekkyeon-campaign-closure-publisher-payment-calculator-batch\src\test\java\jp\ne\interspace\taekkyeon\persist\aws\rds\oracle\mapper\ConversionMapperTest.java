/**
 * Copyright © 2021 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

import com.google.inject.Inject;

import org.junit.Test;
import org.junit.runner.RunWith;

import jp.ne.interspace.taekkyeon.junit.TaekkyeonHsqldbJunitRunner;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonHsqldbOracleJunitModule;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonModules;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonPropertiesJunitModule;
import jp.ne.interspace.taekkyeon.model.PublisherRewardConversionDetails;
import jp.ne.interspace.taekkyeon.model.PublisherRewardId;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertSame;

/**
 * Unit test for {@link ConversionMapper}.
 *
 * <AUTHOR>
 */
@RunWith(TaekkyeonHsqldbJunitRunner.class)
@TaekkyeonModules({ TaekkyeonPropertiesJunitModule.class,
        TaekkyeonHsqldbOracleJunitModule.class })
public class ConversionMapperTest {

    private static final LocalDateTime CLOSED_FROM = LocalDateTime.of(2020, 8, 15, 00, 00);
    private static final LocalDateTime CLOSED_TO = LocalDateTime.of(2020, 8, 25, 00, 00);
    private static final long CAMPAIGN_ID = 1;
    private static final long CAMPAIGN_CLOSURE_ID = 1;

    @Inject
    private ConversionMapper underTest;

    @Test
    public void testFindPublisherRewardsByShouldReturnCorrectDataWhenCalled() {
        // when
        List<PublisherRewardConversionDetails> actual = underTest.findPublisherRewardsBy(
                CLOSED_FROM, CLOSED_TO, CAMPAIGN_ID, CAMPAIGN_CLOSURE_ID);

        // then
        assertEquals(3, actual.size());
        assertFields(actual.get(0), 1L, 1L, 1L, "ID", "ID", 3L, new BigDecimal("420.00"),
                new BigDecimal("600.40"), new BigDecimal("120.00"),
                new BigDecimal("0.40"));
        assertFields(actual.get(1), 1L, 2L, 1L, "ID", "ID", 3L, new BigDecimal("470.00"),
                new BigDecimal("11.20"), new BigDecimal("220.00"),
                new BigDecimal("1.20"));
        assertFields(actual.get(2), 1L, 3L, 1L, "ID", "ID", 3L, new BigDecimal("300.00"),
                new BigDecimal("30.00"), new BigDecimal("0.00"),
                new BigDecimal("0.00"));
    }

    private void assertFields(PublisherRewardConversionDetails conversion,
            Long expectedCampaignClosureId, Long expectedPublisherId,
            Long expectedCampaignId, String expectedMerchantCountryCode,
            String expectedPublisherCountryCode, Long expectedSalesCount,
            BigDecimal expectedReward, BigDecimal expectedRewardInUsd,
            BigDecimal expetedConversionBonus, BigDecimal expetedConversionBonusInUsd) {
        PublisherRewardId publisherRewardId = conversion.getPublisherRewardId();
        assertNotNull(publisherRewardId);
        assertSame(expectedCampaignClosureId, publisherRewardId.getCampaignClosureId());
        assertSame(expectedPublisherId, publisherRewardId.getPublisherId());
        assertSame(expectedCampaignId, publisherRewardId.getCampaignId());
        assertEquals(expectedMerchantCountryCode,
                publisherRewardId.getMerchantCountryCode());
        assertEquals(expectedPublisherCountryCode,
                publisherRewardId.getPublisherCountryCode());
        assertEquals(expectedSalesCount, conversion.getSalesCount());
        assertEquals(expectedReward, conversion.getReward());
        assertEquals(expectedRewardInUsd, conversion.getRewardInUsd());
        assertEquals(expetedConversionBonus, conversion.getConversionBonus());
        assertEquals(expetedConversionBonusInUsd, conversion.getConversionBonusInUsd());
    }
}
