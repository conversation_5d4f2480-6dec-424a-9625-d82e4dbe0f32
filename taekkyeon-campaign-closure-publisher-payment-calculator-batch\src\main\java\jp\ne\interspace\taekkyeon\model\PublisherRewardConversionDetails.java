/**
 * Copyright © 2021 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.model;

import java.math.BigDecimal;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.deser.std.NumberDeserializers.BigDecimalDeserializer;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * DTO for holding the conversion reward details of a publisher.
 *
 * <AUTHOR>
 */
@Getter @AllArgsConstructor
public class PublisherRewardConversionDetails {

    private PublisherRewardId publisherRewardId;
    private Long salesCount;

    @JsonDeserialize(using = BigDecimalDeserializer.class)
    private final BigDecimal reward;

    @JsonDeserialize(using = BigDecimalDeserializer.class)
    private final BigDecimal rewardInUsd;

    @JsonDeserialize(using = BigDecimalDeserializer.class)
    private final BigDecimal conversionBonus;

    @JsonDeserialize(using = BigDecimalDeserializer.class)
    private final BigDecimal conversionBonusInUsd;

    /**
     * Constructor for data from RDS.
     *
     */
    public PublisherRewardConversionDetails(Long publisherId, Long campaignClosureId,
            Long campaignId, String publisherCountryCode, String merchantCountryCode,
            Long salesCount, BigDecimal reward, BigDecimal rewardInUsd,
            BigDecimal conversionBonus, BigDecimal conversionBonusInUsd) {
        this.publisherRewardId = new PublisherRewardId(campaignClosureId, campaignId,
                merchantCountryCode, publisherId, publisherCountryCode);
        this.salesCount = salesCount;
        this.reward = reward;
        this.rewardInUsd = rewardInUsd;
        this.conversionBonus = conversionBonus;
        this.conversionBonusInUsd = conversionBonusInUsd;
    }
}
