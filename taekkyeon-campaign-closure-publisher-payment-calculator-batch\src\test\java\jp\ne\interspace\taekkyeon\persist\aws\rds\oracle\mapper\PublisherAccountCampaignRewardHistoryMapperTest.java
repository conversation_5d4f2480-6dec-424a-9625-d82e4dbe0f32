/**
 * Copyright © 2021 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper;

import java.math.BigDecimal;

import com.google.inject.Inject;

import org.junit.Test;
import org.junit.runner.RunWith;

import jp.ne.interspace.taekkyeon.junit.TaekkyeonHsqldbJunitRunner;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonHsqldbOracleJunitModule;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonModules;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonPropertiesJunitModule;

import static org.junit.Assert.assertEquals;

/**
 * Integration test for {@link PublisherAccountCampaignRewardHistoryMapper}.
 *
 * <AUTHOR>
 */
@RunWith(TaekkyeonHsqldbJunitRunner.class)
@TaekkyeonModules({ TaekkyeonPropertiesJunitModule.class,
        TaekkyeonHsqldbOracleJunitModule.class })
public class PublisherAccountCampaignRewardHistoryMapperTest {

    private static final BigDecimal CONVERSION_BONUS = new BigDecimal("20.00");
    private static final BigDecimal CONVERSION_BONUS_IN_USD = new BigDecimal("0.60");
    private static final BigDecimal FIXED_BONUS = new BigDecimal("10.00");
    private static final BigDecimal FIXED_BONUS_IN_USD = new BigDecimal("0.1");

    @Inject
    private PublisherAccountCampaignRewardHistoryMapper underTest;

    @Test
    public void testDeleteByShouldReturnOneWhenCalled() {
        // given
        Long campaignClosureId = 1L;
        Long publisherId = 1L;

        // when
        int actual = underTest.deleteBy(publisherId, campaignClosureId);

        // then
        assertEquals(1, actual);
    }

    @Test
    public void testInsertHistoryItemByShouldReturnOneWhenCalled() {
        // given
        String targetMonth = "201712";
        Long publisherId = 1L;
        Long campaignId = 2L;
        BigDecimal reward = BigDecimal.ONE;
        BigDecimal rewardInUsd = BigDecimal.TEN;
        Long clickCount = 3L;
        Long impressionCount = 4L;
        Long salesCount = 5L;
        long campaignClosureId = 5;

        // when
        int actual = underTest.insertHistoryItemBy(campaignClosureId, targetMonth,
                publisherId, campaignId, reward, rewardInUsd, clickCount, impressionCount,
                salesCount, CONVERSION_BONUS, CONVERSION_BONUS_IN_USD, FIXED_BONUS,
                FIXED_BONUS_IN_USD);

        // then
        assertEquals(1, actual);
    }
}
