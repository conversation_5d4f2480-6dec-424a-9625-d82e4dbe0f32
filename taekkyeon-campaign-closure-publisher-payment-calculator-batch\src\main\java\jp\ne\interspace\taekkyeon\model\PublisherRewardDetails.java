/**
 * Copyright © 2021 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.model;

import java.math.BigDecimal;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * DTO for holding the reward details of a publisher.
 *
 * <AUTHOR>
 */
@Getter @AllArgsConstructor
public class PublisherRewardDetails {

    private final Long campaignClosureId;
    private final Long publisherId;
    private final String publisherCountryCurrency;
    private final Long campaignId;
    private final String merchantCountryCurrency;
    private BigDecimal reward;
    private BigDecimal rewardInUsd;
    private Long clickCount;
    private Long impressionCount;
    private Long salesCount;
    private BigDecimal conversionBonus;
    private BigDecimal conversionBonusInUsd;
    private BigDecimal fixedBonus;
    private BigDecimal fixedBonusInUsd;
}
