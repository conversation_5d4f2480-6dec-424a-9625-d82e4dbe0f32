/**
 * Copyright © 2021 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.model;

import java.time.LocalDateTime;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * DTO for holding the campaign closure data required for calculating publisher payments.
 *
 * <AUTHOR>
 */
@Getter @AllArgsConstructor
public class CampaignClosure {

    private final long id;
    private final long campaignId;
    private final LocalDateTime closedFrom;
    private final LocalDateTime closedTo;
}
