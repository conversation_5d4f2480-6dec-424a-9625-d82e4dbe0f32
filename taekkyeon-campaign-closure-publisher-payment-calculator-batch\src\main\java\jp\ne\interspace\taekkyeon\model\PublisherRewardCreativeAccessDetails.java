/**
 * Copyright © 2021 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.model;

import java.math.BigDecimal;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * DTO for holding the creative access log summary reward details of a publisher.
 *
 * <AUTHOR>
 */
@Getter @AllArgsConstructor
public class PublisherRewardCreativeAccessDetails {

    private PublisherRewardId publisherRewardId;
    private long clickCount;
    private long impressionCount;
    private BigDecimal clickReward;
    private BigDecimal clickRewardInUsd;

    /**
     * Constructor for data from RDS.
     *
     */
    public PublisherRewardCreativeAccessDetails(Long publisherId, Long campaignClosureId,
            Long campaignId, String publisherCountryCode, String merchantCountryCode,
            Long clickCount, Long impressionCount, BigDecimal clickReward,
            BigDecimal clickRewardInUsd) {
        this.publisherRewardId = new PublisherRewardId(campaignClosureId, campaignId,
                merchantCountryCode, publisherId, publisherCountryCode);
        this.clickCount = clickCount;
        this.impressionCount = impressionCount;
        this.clickReward = clickReward;
        this.clickRewardInUsd = clickRewardInUsd;
    }
}
