/**
 * Copyright © 2022 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.model;

import java.math.BigDecimal;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.deser.std.NumberDeserializers.BigDecimalDeserializer;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * DTO for holding the fixed bonus details of a publisher.
 *
 * <AUTHOR>
 */
@Getter @AllArgsConstructor
public class PublisherFixedBonusDetails {

    private PublisherRewardId publisherRewardId;

    @JsonDeserialize(using = BigDecimalDeserializer.class)
    private final BigDecimal fixedBonus;

    @JsonDeserialize(using = BigDecimalDeserializer.class)
    private final BigDecimal fixedBonusInUsd;

    /**
     * Constructor for data from RDS.
     */
    public PublisherFixedBonusDetails(Long publisherId, Long campaignClosureId,
            Long campaignId, String publisherCountryCode, String merchantCountryCode,
            BigDecimal fixedBonus, BigDecimal fixedBonusInUsd) {
        this.publisherRewardId = new PublisherRewardId(campaignClosureId, campaignId,
                merchantCountryCode, publisherId, publisherCountryCode);
        this.fixedBonus = fixedBonus;
        this.fixedBonusInUsd = fixedBonusInUsd;
    }
}
