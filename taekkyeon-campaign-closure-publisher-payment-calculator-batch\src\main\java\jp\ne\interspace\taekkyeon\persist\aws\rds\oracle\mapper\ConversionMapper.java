/**
 * Copyright © 2021 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

import org.apache.ibatis.annotations.Arg;
import org.apache.ibatis.annotations.ConstructorArgs;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import jp.ne.interspace.taekkyeon.model.PublisherRewardConversionDetails;
import jp.ne.interspace.taekkyeon.multiline.Multiline;

/**
 * Mybat<PERSON> mapper for getting conversion data.
 *
 * <AUTHOR>
 */
public interface ConversionMapper {

    /**
        SELECT
            pa.account_no AS publisherId,
            #{campaignClosureId} AS campaignClosureId,
            sl.merchant_campaign_no AS campaignId,
            pa.country_code AS publisherCountryCode,
            ma.country_code AS merchantCountryCode,
            SUM(sl.sales_reward + sl.total_price_reward + sl.publisher_bonus) AS reward,
            SUM(NVL(sl.publisher_reward_in_usd, 0) + sl.publisher_bonus_in_usd) AS rewardInUsd,
            SUM(sl.sales_count) AS salesCount,
            SUM(sl.publisher_bonus) AS conversionBonus,
            SUM(sl.publisher_bonus_in_usd) conversionBonusInUsd
        FROM
            sales_log sl
        INNER JOIN
            partner_site ps
        ON
            ps.site_no = sl.partner_site_no
        INNER JOIN
            partner_account pa
        ON
            pa.account_no = ps.account_no
        INNER JOIN
            merchant_campaign mc
        ON
            mc.campaign_no = sl.merchant_campaign_no
        INNER JOIN
            merchant_account ma
        ON
            ma.account_no = mc.account_no
        WHERE
            sl.sales_log_status = 1
        AND
            sl.merchant_campaign_no = #{campaignId}
        AND
            sl.confirmed_date >= #{closedFrom, jdbcType=DATE,
            typeHandler = jp.ne.interspace.taekkyeon.persist.aws.rdbms.type.handler.LocalDateTimeTypeHandler}
        AND
            sl.confirmed_date <= #{closedTo, jdbcType=DATE,
            typeHandler = jp.ne.interspace.taekkyeon.persist.aws.rdbms.type.handler.LocalDateTimeTypeHandler}
        AND
            sl.sales_reward + sl.total_price_reward + sl.publisher_bonus > 0
        GROUP BY
            pa.account_no,
            sl.merchant_campaign_no,
            pa.country_code,
            ma.country_code
     */
    @Multiline String SELECT_PUBLISHER_REWARD_CONVERSION_DETAILS = "";

    /**
     * Returns conversion publisher rewards based on the given condition.
     *
     * @param closedFrom
     *          the given closed from
     * @param closedTo
     *          the given closed to
     * @param campaignId
     *          the given campaign id
     * @param campaignClosureId
     *          the given campaign closure id
     * @return conversion publisher rewards based on the given condition
     */
    @Select(SELECT_PUBLISHER_REWARD_CONVERSION_DETAILS)
    @ConstructorArgs({ @Arg(column = "publisherId", javaType = Long.class),
            @Arg(column = "campaignClosureId", javaType = Long.class),
            @Arg(column = "campaignId", javaType = Long.class),
            @Arg(column = "publisherCountryCode", javaType = String.class),
            @Arg(column = "merchantCountryCode", javaType = String.class),
            @Arg(column = "salesCount", javaType = Long.class),
            @Arg(column = "reward", javaType = BigDecimal.class),
            @Arg(column = "rewardInUsd", javaType = BigDecimal.class),
            @Arg(column = "conversionBonus", javaType = BigDecimal.class),
            @Arg(column = "conversionBonusInUsd", javaType = BigDecimal.class)})
    List<PublisherRewardConversionDetails> findPublisherRewardsBy(
            @Param("closedFrom") LocalDateTime closedFrom,
            @Param("closedTo") LocalDateTime closedTo,
            @Param("campaignId") Long campaignId,
            @Param("campaignClosureId") Long campaignClosureId);
}
