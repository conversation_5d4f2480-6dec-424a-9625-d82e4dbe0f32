/**
 * Copyright © 2021 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper;

import java.math.BigDecimal;
import java.time.YearMonth;

import com.google.inject.Inject;

import org.junit.Test;
import org.junit.runner.RunWith;

import jp.ne.interspace.taekkyeon.junit.TaekkyeonHsqldbJunitRunner;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonHsqldbOracleJunitModule;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonModules;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonPropertiesJunitModule;
import jp.ne.interspace.taekkyeon.model.CurrencyExchangeRateCacheKey;

import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;

/**
 * Integration test for {@link CurrencyExchangeRateMapper}.
 *
 * <AUTHOR>
 */
@RunWith(TaekkyeonHsqldbJunitRunner.class)
@TaekkyeonModules({ TaekkyeonPropertiesJunitModule.class,
        TaekkyeonHsqldbOracleJunitModule.class })
public class CurrencyExchangeRateMapperTest {

    @Inject
    private CurrencyExchangeRateMapper underTest;

    @Test
    public void testFindExchangeRateByShouldReturnCorrectExchangeRateWhenCalled() {
        // given
        BigDecimal expected = new BigDecimal("4.05");
        CurrencyExchangeRateCacheKey cacheKey = new CurrencyExchangeRateCacheKey("USD",
                "MYR", YearMonth.of(2019, 3));

        // when
        BigDecimal actual = underTest.findExchangeRateBy(cacheKey);

        // then
        assertNotNull(actual);
        assertTrue(actual.compareTo(expected) == 0);
    }

}
