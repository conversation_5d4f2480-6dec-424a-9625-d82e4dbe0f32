/**
 * Copyright © 2021 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.batch.reader;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;

import org.easybatch.core.record.Header;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;

import jp.ne.interspace.taekkyeon.junit.TaekkyeonMockitoJunitRunner;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonModules;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonPropertiesJunitModule;
import jp.ne.interspace.taekkyeon.model.CampaignClosure;
import jp.ne.interspace.taekkyeon.model.PublisherPaymentPayload;
import jp.ne.interspace.taekkyeon.model.PublisherPaymentRecord;
import jp.ne.interspace.taekkyeon.model.PublisherRewardDetails;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.CampaignClosureMapper;
import jp.ne.interspace.taekkyeon.service.PublisherRewardService;

import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.EMPTY;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertSame;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyZeroInteractions;
import static org.mockito.Mockito.when;
/**
 * Unit test for {@link PublisherPaymentRecordReader}.
 *
 * <AUTHOR>
 */
@RunWith(TaekkyeonMockitoJunitRunner.class)
@TaekkyeonModules(TaekkyeonPropertiesJunitModule.class)
public class PublisherPaymentRecordReaderTest {

    private static final String TARGET_MONTH = "202110";
    private static final long PUBLISHER_ID = 1;
    private static final long CAMPAIGN_ID_2 = 2;
    private static final long CAMPAIGN_ID_3 = 3;
    private static final long CAMPAIGN_CLOSURE_ID = 1;
    private static final long CAMPAIGN_CLOSURE_ID_2 = 2;
    private static final long CAMPAIGN_CLOSURE_ID_3 = 3;

    private static final Date CURRENT_DATE = new Date(0);

    private static final String MERCHANT_COUNTRY_CURRENCY = "IDR";
    private static final String PUBLISHER_COUNTRY_CURRENCY = "SGD";
    private static final BigDecimal REWARD = BigDecimal.ONE;
    private static final BigDecimal REWARD_IN_USD = BigDecimal.TEN;
    private static final BigDecimal CONVERSION_BONUS = new BigDecimal("20.00");
    private static final BigDecimal CONVERSION_BONUS_IN_USD = new BigDecimal("0.60");
    private static final BigDecimal FIXED_BONUS = new BigDecimal("10.00");
    private static final BigDecimal FIXED_BONUS_IN_USD = new BigDecimal("0.1");
    private static final LocalDateTime CLOSED_FROM = LocalDateTime.of(2021, 10, 01, 00, 00);
    private static final LocalDateTime CLOSED_TO = LocalDateTime.of(2021, 10, 05, 00, 00);
    private static final String COUNTRY_CODE = "ID";

    @InjectMocks @Spy
    private PublisherPaymentRecordReader underTest;

    @Mock
    private PublisherRewardService publisherRewardService;

    @Mock
    private Iterator<PublisherPaymentPayload> iterator;

    @Mock
    private CampaignClosureMapper campaignClosureMapper;

    @Test
    public void testOpenShouldYieldCorrectPeriodTimeAndIteratorWithCorrectDataWhenTemporarilyClosedCampaignClosureIsAvailable()
            throws Exception {
        // given
        List<CampaignClosure> campaignClosures = Arrays.asList(new CampaignClosure(
                CAMPAIGN_CLOSURE_ID, CAMPAIGN_ID_2, CLOSED_FROM, CLOSED_TO));
        doReturn(campaignClosures).when(underTest).getCampaignClosures();

        PublisherRewardDetails rewardFromReport = new PublisherRewardDetails(
                CAMPAIGN_CLOSURE_ID, PUBLISHER_ID, PUBLISHER_COUNTRY_CURRENCY,
                CAMPAIGN_ID_2, MERCHANT_COUNTRY_CURRENCY, REWARD, REWARD_IN_USD, 3L, 4L,
                5L, CONVERSION_BONUS, CONVERSION_BONUS_IN_USD, FIXED_BONUS,
                FIXED_BONUS_IN_USD);
        when(publisherRewardService.findPublisherRewardDetailsBy(CAMPAIGN_CLOSURE_ID,
                CAMPAIGN_ID_2, CLOSED_FROM, CLOSED_TO))
                        .thenReturn(Arrays.asList(rewardFromReport));

        // when
        underTest.open();

        // then
        PublisherPaymentPayload actualPublisherPaymentInputPayload = underTest
                .getIterator().next();
        List<PublisherRewardDetails> actualCampaignRewards =
                actualPublisherPaymentInputPayload.getCampaignRewards();
        assertNotNull(actualCampaignRewards);
        assertEquals(1, actualCampaignRewards.size());

        assertEquals(TARGET_MONTH, actualPublisherPaymentInputPayload.getTargetMonth());
    }

    @Test
    public void testOpenShouldYieldNoPeriodTimeAndEmptyIteratorWhenMonthIsNotTemporarilyClosed()
            throws Exception {
        List<CampaignClosure> campaignClosures = Collections.emptyList();
        doReturn(campaignClosures).when(underTest).getCampaignClosures();

        // when
        underTest.open();

        // then
        verifyZeroInteractions(publisherRewardService);
    }

    @Test
    public void testOpenShouldYieldCorrectPeriodTimeAndIteratorWithCorrectDataWhenTemporarilyClosedCampaignClosureIsAvailableForMultipleRewards()
            throws Exception {
        // given
        List<CampaignClosure> campaignClosures = Arrays.asList(new CampaignClosure(
                CAMPAIGN_CLOSURE_ID, CAMPAIGN_ID_2, CLOSED_FROM, CLOSED_TO));
        doReturn(campaignClosures).when(underTest).getCampaignClosures();

        PublisherRewardDetails rewardDetailsOne = new PublisherRewardDetails(
                CAMPAIGN_CLOSURE_ID, PUBLISHER_ID, PUBLISHER_COUNTRY_CURRENCY,
                CAMPAIGN_ID_2, MERCHANT_COUNTRY_CURRENCY, REWARD, REWARD_IN_USD, 3L, 4L,
                5L, CONVERSION_BONUS, CONVERSION_BONUS_IN_USD, FIXED_BONUS,
                FIXED_BONUS_IN_USD);
        PublisherRewardDetails rewardDetailsTwo = new PublisherRewardDetails(
                CAMPAIGN_CLOSURE_ID_2, PUBLISHER_ID, PUBLISHER_COUNTRY_CURRENCY,
                CAMPAIGN_ID_2, MERCHANT_COUNTRY_CURRENCY, REWARD, REWARD_IN_USD, 6L, 7L,
                8L, CONVERSION_BONUS, CONVERSION_BONUS_IN_USD, FIXED_BONUS,
                FIXED_BONUS_IN_USD);
        PublisherRewardDetails rewardDetailsThree = new PublisherRewardDetails(
                CAMPAIGN_CLOSURE_ID_3, PUBLISHER_ID, PUBLISHER_COUNTRY_CURRENCY,
                CAMPAIGN_ID_3, MERCHANT_COUNTRY_CURRENCY, REWARD, REWARD_IN_USD, 3L, 4L,
                5L, CONVERSION_BONUS, CONVERSION_BONUS_IN_USD, FIXED_BONUS,
                FIXED_BONUS_IN_USD);
        when(publisherRewardService.findPublisherRewardDetailsBy(CAMPAIGN_CLOSURE_ID,
                CAMPAIGN_ID_2, CLOSED_FROM, CLOSED_TO))
                        .thenReturn(Arrays.asList(rewardDetailsOne, rewardDetailsTwo,
                                rewardDetailsThree));

        // when
        underTest.open();

        // then
        PublisherPaymentPayload actualPublisherPaymentInputPayload = underTest
                .getIterator().next();
        List<PublisherRewardDetails> actualCampaignRewards =
                actualPublisherPaymentInputPayload.getCampaignRewards();
        assertNotNull(actualCampaignRewards);
        assertEquals(3, actualCampaignRewards.size());

        assertEquals(TARGET_MONTH, actualPublisherPaymentInputPayload.getTargetMonth());
    }

    @Test
    public void testReadRecordShouldReturnCorrectRecordWhenRecordsAreAvailable()
            throws Exception {
        // given
        List<PublisherRewardDetails> campaignRewards = new LinkedList<>();
        PublisherPaymentPayload payload =
                new PublisherPaymentPayload(TARGET_MONTH, campaignRewards,
                        CAMPAIGN_CLOSURE_ID);
        doReturn(iterator).when(underTest).getIterator();
        when(iterator.next()).thenReturn(payload);
        when(iterator.hasNext()).thenReturn(true);
        doReturn(CURRENT_DATE).when(underTest).getCurrentDate();
        Header expectedHeader = new Header(null, null, CURRENT_DATE);

        // when
        PublisherPaymentRecord actual = underTest.readRecord();

        // then
        assertNotNull(actual);
        assertFields(expectedHeader, actual.getHeader());
        assertNotNull(actual.getPayload());
        assertEquals(TARGET_MONTH, actual.getPayload().getTargetMonth());
        assertEquals(campaignRewards, actual.getPayload().getCampaignRewards());
    }

    @Test
    public void testGetCampaignClosuresShouldReturnCorrectDataWhenGivenParameterCountryCodeIsNotEmpty() {
        // given
        List<CampaignClosure> expected = Arrays.asList(mock(CampaignClosure.class));
        doReturn(COUNTRY_CODE).when(underTest).getPaymentCountryCode();
        when(campaignClosureMapper.findCampaignClosuresBy(COUNTRY_CODE))
                .thenReturn(expected);

        // when
        List<CampaignClosure> actual = underTest.getCampaignClosures();

        // then
        assertSame(expected, actual);
        verify(campaignClosureMapper, never()).findCampaignClosuresByCloseTemporaryFlag();
    }

    @Test
    public void testGetCampaignClosuresShouldReturnCorrectDataWhenGivenParameterCountryCodeIsEmpty() {
        // given
        List<CampaignClosure> expected = Arrays.asList(mock(CampaignClosure.class));
        doReturn(EMPTY).when(underTest).getPaymentCountryCode();
        when(campaignClosureMapper.findCampaignClosuresByCloseTemporaryFlag())
                .thenReturn(expected);

        // when
        List<CampaignClosure> actual = underTest.getCampaignClosures();

        // then
        assertSame(expected, actual);
        verify(campaignClosureMapper, never()).findCampaignClosuresBy(anyString());
    }

    @Test
    public void testGetCampaignClosuresShouldReturnCorrectDataWhenGivenParameterCountryCodeIsNull() {
        // given
        List<CampaignClosure> expected = Arrays.asList(mock(CampaignClosure.class));
        doReturn(null).when(underTest).getPaymentCountryCode();
        when(campaignClosureMapper.findCampaignClosuresByCloseTemporaryFlag())
                .thenReturn(expected);

        // when
        List<CampaignClosure> actual = underTest.getCampaignClosures();

        // then
        assertSame(expected, actual);
        verify(campaignClosureMapper, never()).findCampaignClosuresBy(anyString());
    }

    private void assertFields(Header expected, Header actual) {
        assertNotNull(actual);
        assertEquals(expected.getNumber(), actual.getNumber());
        assertEquals(expected.getSource(), actual.getSource());
        assertEquals(expected.getCreationDate(), actual.getCreationDate());
    }

}
