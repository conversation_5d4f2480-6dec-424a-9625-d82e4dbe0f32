/**
 * Copyright © 2021 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.batch;

import java.math.BigDecimal;
import java.time.YearMonth;
import java.time.ZonedDateTime;
import java.util.List;

import com.google.inject.Inject;

import org.easybatch.core.record.Batch;
import org.easybatch.core.record.Record;
import org.junit.Test;
import org.junit.runner.RunWith;

import jp.ne.interspace.taekkyeon.batch.processor.DummyRecordProcessor;
import jp.ne.interspace.taekkyeon.batch.reader.PublisherPaymentRecordReader;
import jp.ne.interspace.taekkyeon.batch.writer.PublisherPaymentRecordWriter;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonHsqldbOracleJunitModule;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonIntegrationTestHsqldbJunitRunner;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonModules;
import jp.ne.interspace.taekkyeon.model.IntegrationTestAccountPaymentHistory;
import jp.ne.interspace.taekkyeon.model.IntegrationTestCampaignRewardHistory;
import jp.ne.interspace.taekkyeon.model.PaymentStatus;
import jp.ne.interspace.taekkyeon.model.PublisherPaymentRecord;
import jp.ne.interspace.taekkyeon.module.PublisherPaymentJunitModule;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.IntegrationTestMapper;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

/**
 * Integration test for the publisher payment calculator batch with Rds mode.
 *
 * <AUTHOR>
 */
@RunWith(TaekkyeonIntegrationTestHsqldbJunitRunner.class)
@TaekkyeonModules({ PublisherPaymentJunitModule.class,
        TaekkyeonHsqldbOracleJunitModule.class })
public class RdsPublisherPaymentCalculatorIntegrationTest {

    private static final long PUBLISHER_ID_3 = 3;
    private static final long CAMPAIGN_ID_5 = 5;
    private static final long IMPRESSION_COUNT_50 = 50;
    private static final long CLICK_COUNT_40 = 40;
    private static final long SALES_COUNT_60 = 60;
    private static final BigDecimal REWARD_30 = new BigDecimal("30.00");
    private static final BigDecimal REWARD_IN_USD_60 = new BigDecimal("60.00");
    private static final long PUBLISHER_ID_2 = 2;
    private static final long CAMPAIGN_ID_10 = 10;
    private static final long IMPRESSION_COUNT_55 = 55;
    private static final long CLICK_COUNT_45 = 45;
    private static final long SALES_COUNT_65 = 65;
    private static final BigDecimal REWARD_35 = new BigDecimal("35.00");
    private static final BigDecimal REWARD_IN_USD_70 = new BigDecimal("70.00");
    private static final long PUBLISHER_ID_1 = 1;
    private static final long CAMPAIGN_ID_140 = 140;
    private static final long CAMPAIGN_ID_141 = 141;
    private static final String CREATED_BY_BATCH = "PublisherPaymentCalculatorBatch";
    private static final PaymentStatus STATUS_UNPAID = PaymentStatus.UNPAID;
    private static final long CAMPAIGN_CLOSURE_ID = 1;
    private static final long CAMPAIGN_CLOSURE_ID_2 = 2;
    private static final long CAMPAIGN_CLOSURE_ID_3 = 3;
    private static final long CAMPAIGN_CLOSURE_ID_4 = 4;
    private static final long IMPRESSION_COUNT_1 = 1;
    private static final long CLICK_COUNT_1 = 1;
    private static final long SALES_COUNT_6 = 6;
    private static final long PUBLISHER_ID_6 = 6;
    private static final BigDecimal REWARD_2030 = new BigDecimal("2030.00");
    private static final BigDecimal REWARD_IN_USD_100660 = new BigDecimal("1006.60");
    private static final String CREATED_BY_BATCH_2 = "PublisherCampaignClosurePaymentCalculatorBatch";
    private static final BigDecimal ZERO = BigDecimal.ZERO.setScale(2);
    private static final BigDecimal CONVERSION_BONUS_5 = new BigDecimal("500.00");
    private static final BigDecimal CONVERSION_BONUS_IN_USD_5 = new BigDecimal("3.10");
    private static final BigDecimal FIXED_BONUS_5 = new BigDecimal("630.00");
    private static final BigDecimal FIXED_BONUS_IN_USD_5 = new BigDecimal("3.50");

    @Inject
    private PublisherPaymentRecordReader recordReader;

    @Inject
    private DummyRecordProcessor recordProcessor;

    @Inject
    private PublisherPaymentRecordWriter recordWriter;

    @Inject
    private IntegrationTestMapper testMapper;

    @Test
    public void testPublisherPaymentCalculatorBatch() throws Exception {
        // when
        recordReader.open();
        recordWriter.open();
        while (true) {
            PublisherPaymentRecord paymentRecordRecord = recordReader.readRecord();
            if (paymentRecordRecord == null) {
                break;
            }
            @SuppressWarnings("rawtypes")
            Record paymentProcessorRecord = recordProcessor
                    .processRecord(paymentRecordRecord);
            recordWriter.writeRecords(new Batch(paymentProcessorRecord));
        }
        recordWriter.close();

        // then
        List<IntegrationTestCampaignRewardHistory> rdsCampaignRewardHistoryActual =
                testMapper.findAllCampaignRewardHistories();
        assertNotNull(rdsCampaignRewardHistoryActual);
        assertEquals(5, rdsCampaignRewardHistoryActual.size());
        IntegrationTestCampaignRewardHistory campaignRewardHistory1 =
                rdsCampaignRewardHistoryActual.get(0);
        assertCampaignRewardHistory(campaignRewardHistory1, CAMPAIGN_CLOSURE_ID,
                PUBLISHER_ID_1,
                CAMPAIGN_ID_140, campaignRewardHistory1.getRewardMonth(),
                IMPRESSION_COUNT_50, CLICK_COUNT_40, SALES_COUNT_60, REWARD_30,
                REWARD_IN_USD_60, ZERO, ZERO, ZERO, ZERO, CREATED_BY_BATCH,
                campaignRewardHistory1.getCreatedOn());
        IntegrationTestCampaignRewardHistory campaignRewardHistory2 =
                rdsCampaignRewardHistoryActual.get(1);
        assertCampaignRewardHistory(campaignRewardHistory2, CAMPAIGN_CLOSURE_ID_4,
                PUBLISHER_ID_2, CAMPAIGN_ID_10, campaignRewardHistory2.getRewardMonth(),
                IMPRESSION_COUNT_55, CLICK_COUNT_45, SALES_COUNT_65, REWARD_35,
                REWARD_IN_USD_70, ZERO, ZERO, ZERO, ZERO, CREATED_BY_BATCH,
                campaignRewardHistory2.getCreatedOn());
        IntegrationTestCampaignRewardHistory campaignRewardHistory3 =
                rdsCampaignRewardHistoryActual.get(2);
        assertCampaignRewardHistory(campaignRewardHistory3, CAMPAIGN_CLOSURE_ID_2,
                PUBLISHER_ID_2, CAMPAIGN_ID_141, campaignRewardHistory3.getRewardMonth(),
                IMPRESSION_COUNT_50, CLICK_COUNT_40, SALES_COUNT_60, REWARD_30,
                REWARD_IN_USD_60, ZERO, ZERO, ZERO, ZERO, CREATED_BY_BATCH,
                campaignRewardHistory3.getCreatedOn());
        IntegrationTestCampaignRewardHistory campaignRewardHistory4 =
                rdsCampaignRewardHistoryActual.get(3);
        assertCampaignRewardHistory(campaignRewardHistory4, CAMPAIGN_CLOSURE_ID_3,
                PUBLISHER_ID_3, CAMPAIGN_ID_5, campaignRewardHistory4.getRewardMonth(),
                IMPRESSION_COUNT_50, CLICK_COUNT_40, SALES_COUNT_60, REWARD_30,
                REWARD_IN_USD_60, ZERO, ZERO, ZERO, ZERO, CREATED_BY_BATCH,
                campaignRewardHistory4.getCreatedOn());
        IntegrationTestCampaignRewardHistory campaignRewardHistory5 = rdsCampaignRewardHistoryActual
                .get(4);
        assertCampaignRewardHistory(campaignRewardHistory5, CAMPAIGN_CLOSURE_ID_4,
                PUBLISHER_ID_6, CAMPAIGN_ID_10, campaignRewardHistory5.getRewardMonth(),
                IMPRESSION_COUNT_1, CLICK_COUNT_1, SALES_COUNT_6, REWARD_2030,
                REWARD_IN_USD_100660, CONVERSION_BONUS_5, CONVERSION_BONUS_IN_USD_5,
                FIXED_BONUS_5, FIXED_BONUS_IN_USD_5, CREATED_BY_BATCH_2,
                campaignRewardHistory5.getCreatedOn());

        List<IntegrationTestAccountPaymentHistory> rdsAccountPaymentHistoryActual =
                testMapper.findAllAccountPaymentHistories();
        assertNotNull(rdsAccountPaymentHistoryActual);
        assertEquals(5, rdsAccountPaymentHistoryActual.size());
        IntegrationTestAccountPaymentHistory accountPaymentHistory1 =
                rdsAccountPaymentHistoryActual.get(0);
        assertIntegrationTestAccountPaymentHistory(accountPaymentHistory1, PUBLISHER_ID_1,
                accountPaymentHistory1.getRewardMonth(), null, null, STATUS_UNPAID,
                CREATED_BY_BATCH, accountPaymentHistory1.getCreatedOn());
        IntegrationTestAccountPaymentHistory accountPaymentHistory2 =
                rdsAccountPaymentHistoryActual.get(1);
        assertIntegrationTestAccountPaymentHistory(accountPaymentHistory2, PUBLISHER_ID_2,
                accountPaymentHistory2.getRewardMonth(), null, null, STATUS_UNPAID,
                CREATED_BY_BATCH, accountPaymentHistory2.getCreatedOn());
        IntegrationTestAccountPaymentHistory accountPaymentHistory3 =
                rdsAccountPaymentHistoryActual.get(2);
        assertIntegrationTestAccountPaymentHistory(accountPaymentHistory3, PUBLISHER_ID_2,
                accountPaymentHistory3.getRewardMonth(), null, null, STATUS_UNPAID,
                CREATED_BY_BATCH,
                accountPaymentHistory3.getCreatedOn());
        IntegrationTestAccountPaymentHistory accountPaymentHistory4 =
                rdsAccountPaymentHistoryActual.get(3);
        assertIntegrationTestAccountPaymentHistory(accountPaymentHistory4, PUBLISHER_ID_3,
                accountPaymentHistory4.getRewardMonth(), null, null, STATUS_UNPAID,
                CREATED_BY_BATCH, accountPaymentHistory4.getCreatedOn());
        IntegrationTestAccountPaymentHistory accountPaymentHistory5 = rdsAccountPaymentHistoryActual
                .get(4);
        assertIntegrationTestAccountPaymentHistory(accountPaymentHistory5, PUBLISHER_ID_6,
                accountPaymentHistory5.getRewardMonth(), null, null, STATUS_UNPAID,
                CREATED_BY_BATCH_2, accountPaymentHistory5.getCreatedOn());
    }

    private void assertCampaignRewardHistory(IntegrationTestCampaignRewardHistory actual,
            Long campaignClosureId, Long publisherId, Long campaignId,
            YearMonth rewardMonth, Long impressionCount, Long clickCount, Long salesCount,
            BigDecimal reward, BigDecimal rewardInUsd, BigDecimal conversionBonus,
            BigDecimal conversionBonusInUsd, BigDecimal fixedBonus,
            BigDecimal fixedBonusInUsd, String createdBy, ZonedDateTime createdOn) {
        assertNotNull(actual);
        assertEquals(campaignClosureId, actual.getCampaignClosureId());
        assertEquals(publisherId, actual.getPublisherId());
        assertEquals(campaignId, actual.getCampaignId());
        assertEquals(rewardMonth, actual.getRewardMonth());
        assertEquals(impressionCount, actual.getImpressionCount());
        assertEquals(clickCount, actual.getClickCount());
        assertEquals(salesCount, actual.getSalesCount());
        assertEquals(reward, actual.getReward());
        assertEquals(rewardInUsd, actual.getRewardInUsd());
        assertEquals(conversionBonus, actual.getConversionBonus());
        assertEquals(conversionBonusInUsd, actual.getConversionBonusInUsd());
        assertEquals(fixedBonus, actual.getFixedBonus());
        assertEquals(fixedBonusInUsd, actual.getFixedBonusInUsd());
        assertEquals(createdBy, actual.getCreatedBy());
        assertNotNull(createdOn);
    }

    private void assertIntegrationTestAccountPaymentHistory(
            IntegrationTestAccountPaymentHistory actual, Long publisherId,
            YearMonth rewardMonth, BigDecimal reward, BigDecimal rewardInUsd,
            PaymentStatus status, String createdBy, ZonedDateTime createdOn) {
        assertNotNull(actual);
        assertEquals(publisherId, actual.getPublisherId());
        assertEquals(rewardMonth, actual.getRewardMonth());
        assertEquals(reward, actual.getReward());
        assertEquals(rewardInUsd, actual.getRewardInUsd());
        assertEquals(status, actual.getPaymentStatus());
        assertEquals(createdBy, actual.getCreatedBy());
        assertNotNull(createdOn);
    }
}
