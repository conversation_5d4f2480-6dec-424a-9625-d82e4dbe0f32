/**
 * Copyright © 2021 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.model;

import java.util.LinkedList;
import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Payload for the {@link PublisherPaymentRecord}s.
 *
 * <AUTHOR>
 */
@Getter @AllArgsConstructor
public class PublisherPaymentPayload {

    private final String targetMonth;
    private List<PublisherRewardDetails> campaignRewards = new LinkedList<>();
    private long campaignClosureId;
}
