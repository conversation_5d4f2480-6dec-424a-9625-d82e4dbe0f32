/**
 * Copyright © 2021 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper;

import java.math.BigDecimal;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import jp.ne.interspace.taekkyeon.model.CurrencyExchangeRateCacheKey;
import jp.ne.interspace.taekkyeon.multiline.Multiline;

/**
 * MyBatis mapper for handling currency exchange rate.
 *
 * <AUTHOR>
 */
public interface CurrencyExchangeRateMapper {

    /**
        SELECT
          rate
        FROM
          currency_exchange_rate_history
        WHERE
          currency = #{cacheKey.baseCurrency}
        AND
          quote_currency = #{cacheKey.quoteCurrency}
        AND
          target_month = #{cacheKey.targetMonth, jdbcType=DATE,
          typeHandler=jp.ne.interspace.taekkyeon.persist.aws.rdbms.type.handler.TimestampYearMonthTypeHandler}
     */
    @Multiline String SELECT_EXCHANGE_RATE = "";

    /**
     * Returns the exchange rate for the given condition.
     *
     * @param cacheKey
     *          the given cache key
     * @return the exchange rate for the given condition
     */
    @Select(SELECT_EXCHANGE_RATE)
    BigDecimal findExchangeRateBy(
            @Param("cacheKey") CurrencyExchangeRateCacheKey cacheKey);
}
