/**
 * Copyright © 2021 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.model;

import org.easybatch.core.record.GenericRecord;
import org.easybatch.core.record.Header;
import org.easybatch.core.record.Record;

/**
 * {@link Record} implementation to hold publisher payment information.
 *
 * <AUTHOR>
 */
public class PublisherPaymentRecord
        extends GenericRecord<PublisherPaymentPayload> {

    public PublisherPaymentRecord(Header header,
            PublisherPaymentPayload payload) {
        super(header, payload);
    }
}
