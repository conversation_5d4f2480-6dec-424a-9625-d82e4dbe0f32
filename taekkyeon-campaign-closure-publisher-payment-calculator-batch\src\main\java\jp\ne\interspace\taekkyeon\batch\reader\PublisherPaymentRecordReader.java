/**
 * Copyright © 2021 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.batch.reader;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.Strings;
import com.google.inject.Inject;
import com.google.inject.name.Named;

import lombok.Getter;

import org.easybatch.core.reader.RecordReader;
import org.easybatch.core.record.Header;

import jp.ne.interspace.taekkyeon.common.TaekkyeonConstants;
import jp.ne.interspace.taekkyeon.model.CampaignClosure;
import jp.ne.interspace.taekkyeon.model.PublisherPaymentPayload;
import jp.ne.interspace.taekkyeon.model.PublisherPaymentRecord;
import jp.ne.interspace.taekkyeon.model.PublisherRewardDetails;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.CampaignClosureMapper;
import jp.ne.interspace.taekkyeon.service.PublisherRewardService;

import static java.util.Collections.emptyList;
import static jp.ne.interspace.taekkyeon.module.PublisherPaymentCalculatorModule.BIND_KEY_PAYMENT_COUNTRY_CODE;
import static lombok.AccessLevel.PACKAGE;

/**
 * {@link RecordReader} implementation for reading publisher payment data.
 *
 * <AUTHOR>
 */
public class PublisherPaymentRecordReader implements RecordReader {

    @Inject
    private PublisherRewardService publisherRewardService;

    @Inject
    private CampaignClosureMapper campaignClosureMapper;

    @VisibleForTesting @Getter(PACKAGE)
    private Iterator<PublisherPaymentPayload> iterator =
            new LinkedList<PublisherPaymentPayload>().iterator();

    @VisibleForTesting @Getter(PACKAGE)
    private long currentRecordNumber;

    @Inject @Named(BIND_KEY_PAYMENT_COUNTRY_CODE) @VisibleForTesting @Getter(PACKAGE)
    private String paymentCountryCode;

    @Override
    public void open() throws Exception {
        initializePublisherRewardCache();
    }

    @Override
    public PublisherPaymentRecord readRecord() throws Exception {
        return getNextRecord();
    }

    @Override
    public void close() throws Exception {
        // do nothing
    }

    @VisibleForTesting
    Date getCurrentDate() {
        return new Date();
    }

    @VisibleForTesting
    List<CampaignClosure> getCampaignClosures() {
        String countryCode = getPaymentCountryCode();
        if (Strings.isNullOrEmpty(countryCode)) {
            return campaignClosureMapper.findCampaignClosuresByCloseTemporaryFlag();
        }
        return campaignClosureMapper.findCampaignClosuresBy(countryCode);
    }

    private void initializePublisherRewardCache() {
        List<PublisherPaymentPayload> publisherPaymentInputPayloads =
                new LinkedList<>();
        List<CampaignClosure> campaignClosures = getCampaignClosures();
        for (CampaignClosure campaignClosure : campaignClosures) {
            LocalDateTime closedFrom = campaignClosure.getClosedFrom();
            LocalDateTime closedTo = campaignClosure.getClosedTo();
            List<PublisherRewardDetails> publisherRewardDetails = publisherRewardService
                    .findPublisherRewardDetailsBy(campaignClosure.getId(),
                            campaignClosure.getCampaignId(), closedFrom, closedTo);
            if (publisherRewardDetails != null && !publisherRewardDetails.isEmpty()) {
                publisherPaymentInputPayloads.add(new PublisherPaymentPayload(
                        getTargetMonth(closedFrom), publisherRewardDetails,
                        campaignClosure.getId()));
            } else {
                publisherPaymentInputPayloads.add(new PublisherPaymentPayload(
                        getTargetMonth(closedFrom), emptyList(),
                        campaignClosure.getId()));
            }
        }
        iterator = publisherPaymentInputPayloads.iterator();
    }

    private PublisherPaymentRecord getNextRecord() {
        if (getIterator().hasNext()) {
            return new PublisherPaymentRecord(
                    new Header(null, null, getCurrentDate()),
                    getIterator().next());
        }
        return null;
    }

    private String getTargetMonth(LocalDateTime targetTime) {
        DateTimeFormatter formatter = DateTimeFormatter
                .ofPattern(TaekkyeonConstants.DATE_FORMAT_YYYYMM);
        return targetTime.format(formatter);
    }
}
