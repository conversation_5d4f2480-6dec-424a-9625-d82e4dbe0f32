/**
 * Copyright © 2022 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

import com.google.inject.Inject;

import org.junit.Test;
import org.junit.runner.RunWith;

import jp.ne.interspace.taekkyeon.junit.TaekkyeonHsqldbJunitRunner;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonHsqldbOracleJunitModule;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonModules;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonPropertiesJunitModule;
import jp.ne.interspace.taekkyeon.model.PublisherFixedBonusDetails;
import jp.ne.interspace.taekkyeon.model.PublisherRewardId;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertSame;

/**
 * Integration test for {@link BonusMapper}.
 *
 * <AUTHOR>
 */
@RunWith(TaekkyeonHsqldbJunitRunner.class)
@TaekkyeonModules({ TaekkyeonPropertiesJunitModule.class,
        TaekkyeonHsqldbOracleJunitModule.class })
public class BonusMapperTest {

    private static final LocalDateTime CLOSED_FROM = LocalDateTime.of(2017, 10, 1, 00,
            00);
    private static final LocalDateTime CLOSED_TO = LocalDateTime.of(2017, 11, 25, 00, 00);
    private static final long CAMPAIGN_ID = 1;
    private static final long CAMPAIGN_CLOSURE_ID = 1;

    @Inject
    private BonusMapper underTest;

    @Test
    public void testFindPublisherBonusesByShouldReturnCorrectDataWhenCalled() {
        // when
        List<PublisherFixedBonusDetails> actual = underTest.findPublisherBonusesBy(
                CLOSED_FROM, CLOSED_TO, CAMPAIGN_ID, CAMPAIGN_CLOSURE_ID);

        // then
        assertNotNull(actual);
        assertEquals(2, actual.size());
        assertFields(actual.get(0), 1L, 1L, 1L, "ID", "ID", new BigDecimal("200.00"),
                new BigDecimal("1.40"));
        assertFields(actual.get(1), 1L, 2L, 1L, "ID", "ID", new BigDecimal("430.00"),
                new BigDecimal("2.10"));
    }

    private void assertFields(PublisherFixedBonusDetails conversion,
            Long expectedCampaignClosureId, Long expectedPublisherId,
            Long expectedCampaignId, String expectedMerchantCountryCode,
            String expectedPublisherCountryCode, BigDecimal expectedFixedBonus,
            BigDecimal expectedFixedBonusInUsd) {
        PublisherRewardId publisherRewardId = conversion.getPublisherRewardId();
        assertNotNull(publisherRewardId);
        assertSame(expectedCampaignClosureId, publisherRewardId.getCampaignClosureId());
        assertSame(expectedPublisherId, publisherRewardId.getPublisherId());
        assertSame(expectedCampaignId, publisherRewardId.getCampaignId());
        assertEquals(expectedMerchantCountryCode,
                publisherRewardId.getMerchantCountryCode());
        assertEquals(expectedPublisherCountryCode,
                publisherRewardId.getPublisherCountryCode());
        assertEquals(expectedFixedBonus, conversion.getFixedBonus());
        assertEquals(expectedFixedBonusInUsd, conversion.getFixedBonusInUsd());
    }
}
