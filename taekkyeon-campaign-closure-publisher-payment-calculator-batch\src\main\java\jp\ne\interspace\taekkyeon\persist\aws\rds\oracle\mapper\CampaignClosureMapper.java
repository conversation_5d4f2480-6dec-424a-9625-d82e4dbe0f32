/**
 * Copyright © 2021 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper;

import java.time.LocalDateTime;
import java.util.List;

import org.apache.ibatis.annotations.Arg;
import org.apache.ibatis.annotations.ConstructorArgs;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import jp.ne.interspace.taekkyeon.model.CampaignClosure;
import jp.ne.interspace.taekkyeon.model.PaymentGenerationStatus;
import jp.ne.interspace.taekkyeon.multiline.Multiline;

/**
 * MyBatis mapper for getting the campaign closure data.
 *
 * <AUTHOR>
 */
public interface CampaignClosureMapper {

    /**
        SELECT
            cc.id,
            cc.campaign_id campaignId,
            cc.closed_from closedFrom,
            cc.closed_to closedTo
        FROM
            campaign_closure cc
        INNER JOIN
            merchant_campaign mc
        ON
            cc.campaign_id = mc.campaign_no
        INNER JOIN
            MERCHANT_ACCOUNT ma
        ON
            ma.account_no = mc.account_no
        WHERE
            ma.country_code = #{countryCode}
        AND
            cc.is_payment_generated = 0
        AND
            cc.status = 2
     */
    @Multiline String SELECT_CAMPAIGN_CLOSURES_BY_COUNTRY_CODE = "";

    /**
     * Returns the closed campaign closure are not generated by specified country code.
     *
     * @param countryCode
     *          the given country code
     * @return the closed campaign closure are not generated by specified country code
     */
    @Select(SELECT_CAMPAIGN_CLOSURES_BY_COUNTRY_CODE)
    @ConstructorArgs({ @Arg(column = "id", javaType = long.class),
            @Arg(column = "campaignId", javaType = long.class),
            @Arg(column = "closedFrom", javaType = LocalDateTime.class),
            @Arg(column = "closedTo", javaType = LocalDateTime.class) })
    List<CampaignClosure> findCampaignClosuresBy(
            @Param("countryCode") String countryCode);

    /**
        SELECT
            cc.id,
            cc.campaign_id campaignId,
            cc.closed_from closedFrom,
            cc.closed_to closedTo
        FROM
            campaign_closure cc
        INNER JOIN
            merchant_campaign mc
        ON
            cc.campaign_id = mc.campaign_no
        INNER JOIN
            MERCHANT_ACCOUNT ma
        ON
            ma.account_no = mc.account_no
        INNER JOIN
            MONTHLY_CLOSING mcl
        ON
            mcl.country_code = ma.country_code
        WHERE
            mcl.temporary_closing_flag = 1
        AND
            cc.is_payment_generated = 0
        AND
            cc.status = 2
     */
    @Multiline String SELECT_CAMPAIGN_CLOSURES_BY_CLOSE_TEMPORARY_FLAG = "";

    /**
     * Returns the closed campaign closure are not generated by close temporary flag.
     *
     * @return the closed campaign closure are not generated by close temporary flag
     */
    @Select(SELECT_CAMPAIGN_CLOSURES_BY_CLOSE_TEMPORARY_FLAG)
    @ConstructorArgs({ @Arg(column = "id", javaType = long.class),
            @Arg(column = "campaignId", javaType = long.class),
            @Arg(column = "closedFrom", javaType = LocalDateTime.class),
            @Arg(column = "closedTo", javaType = LocalDateTime.class) })
    List<CampaignClosure> findCampaignClosuresByCloseTemporaryFlag();

    /**
        UPDATE
            campaign_closure
        SET
            is_payment_generated = #{status.value},
            updated_on = SYSDATE,
            updated_by = 'CampaignClosurePublisherPaymentCalculatorBatch'
        WHERE
            status = 2
        AND
            is_payment_generated = 0
        AND
            id = #{id}
     */
    @Multiline String UPDATE_IS_PAYMENT_GENERATED = "";

    /**
     * Updates is payment generated flag.
     *
     * @param id
     *          the given campaign closure id
     * @param status
     *          the given campaign closure status
     * @return number of updated
     */
    @Update(UPDATE_IS_PAYMENT_GENERATED)
    int updateIsPaymentGenerated(@Param("id") long id,
            @Param("status") PaymentGenerationStatus status);
}
