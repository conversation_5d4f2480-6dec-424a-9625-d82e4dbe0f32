/**
 * Copyright © 2021 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.model;

import java.math.BigDecimal;
import java.time.YearMonth;
import java.time.ZonedDateTime;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * DTO for holding the data of campaign reward history.
 *
 * <AUTHOR>
 */
@AllArgsConstructor @Getter
public class IntegrationTestCampaignRewardHistory {

    private final Long campaignClosureId;
    private final Long publisherId;
    private final Long campaignId;
    private final YearMonth rewardMonth;
    private final Long impressionCount;
    private final Long clickCount;
    private final Long salesCount;
    private final BigDecimal reward;
    private final BigDecimal rewardInUsd;
    private final BigDecimal conversionBonus;
    private final BigDecimal conversionBonusInUsd;
    private final BigDecimal fixedBonus;
    private final BigDecimal fixedBonusInUsd;
    private final String createdBy;
    private final ZonedDateTime createdOn;
}
