SET DATABASE SQL SYNTAX ORA TRUE;

DROP TABLE IF EXISTS BANNER_ACCESS_LOG_SUMMARY;
CREATE TABLE BANNER_ACCESS_LOG_SUMMARY (
    "BANNER_ID" NUMBER(10,0) NOT NULL, 
    "MERCHANT_CAMPAIGN_NO" NUMBER(10,0) NOT NULL, 
    "LOG_DATE" DATE NOT NULL, 
    "PARTNER_SITE_NO" NUMBER(10,0) NOT NULL, 
    "RANK" NUMBER(2,0), 
    "RESULT_ID" NUMBER(4,0) NOT NULL, 
    "TRACKING_SESSION_ID" VARCHAR2(40 BYTE), 
    "IMPRESSION_COUNT" NUMBER(10,0) NOT NULL, 
    "CLICK_COUNT" NUMBER(10,0) NOT NULL, 
    "REWARD_TYPE" NUMBER(1,0) NOT NULL, 
    "CLICK_REWARD" NUMBER(10,2) NOT NULL,
    "CLICK_REWARD_IN_USD" NUMBER(10,2), 
    "COMMISSION_TYPE" NUMBER(1,0) NOT NULL, 
    "AT_COMMISSION" NUMBER(10,2) NOT NULL, 
    "AGENT_COMMISSION" NUMBER(10,2) NOT NULL, 
    "REWARD_EDIT_DATE" DATE, 
    "DEVICE_TYPE" NUMBER(2,0) NOT NULL, 
    "CREATED_BY" VARCHAR2(256 BYTE), 
    "CREATED_ON" DATE, 
    "UPDATED_BY" VARCHAR2(256 BYTE), 
    "UPDATED_ON" DATE, 
    "P_AGENT_COMMISSION" NUMBER(10,2) DEFAULT 0 NOT NULL, 
    "GOODS_ID" VARCHAR2(256 BYTE) DEFAULT ' ', 
    "CATEGORY_ID" VARCHAR2(250 BYTE)
);

DROP TABLE IF EXISTS CAMPAIGN_CLOSURE;
CREATE TABLE CAMPAIGN_CLOSURE (
    "ID" NUMBER(10,0) NOT NULL,
    "CAMPAIGN_ID" NUMBER(10,0) NOT NULL,
    "STATUS" NUMBER(2,0),
    "CLOSED_FROM" DATE,
    "CLOSED_TO" DATE,
    "APPROVED_TOTAL_COMMISSION" NUMBER(20,0) DEFAULT 0,
    "APPROVED_AT_COMMISSION" NUMBER(20,0) DEFAULT 0,
    "APPROVED_PUBLISHER_REWARD" NUMBER(20,0) DEFAULT 0,
    "APPROVED_CONVERSION" NUMBER(20,0) DEFAULT 0,
    "APPROVED_TRANSACTION_ITEMS" NUMBER(20,0) DEFAULT 0,
    "CREATED_ON" DATE,
    "CREATED_BY" VARCHAR2(256),
    "UPDATED_ON" DATE,
    "UPDATED_BY" VARCHAR2(256),
    "IS_PAYMENT_GENERATED" NUMBER(1,0) DEFAULT 0
);

DROP TABLE IF EXISTS PUBLISHER_ACCOUNT_CAMPAIGN_REWARD_HISTORY;
CREATE TABLE PUBLISHER_ACCOUNT_CAMPAIGN_REWARD_HISTORY (
    "ID" NUMBER(10,0) NOT NULL,
    "PUBLISHER_ID" NUMBER(10,0) NOT NULL,
    "CAMPAIGN_ID" NUMBER(10,0) NOT NULL,
    "CAMPAIGN_CLOSURE_ID" NUMBER(10, 0) NOT NULL,
    "REWARD_MONTH" DATE NOT NULL,
    "IMPRESSION_COUNT" NUMBER(10,0), 
    "CLICK_COUNT" NUMBER(10,0), 
    "SALES_COUNT" NUMBER(10,0), 
    "AMOUNT" NUMBER(15,2), 
    "CREATED_BY" VARCHAR2(64), 
    "CREATED_ON" DATE, 
    "UPDATED_BY" VARCHAR2(64), 
    "UPDATED_ON" DATE, 
    "AMOUNT_IN_USD" NUMBER(20,2),
    "CONVERSION_BONUS" NUMBER(12, 2) DEFAULT 0 NOT NULL,
	"CONVERSION_BONUS_IN_USD" NUMBER(12, 2) DEFAULT 0 NOT NULL,
	"FIXED_BONUS" NUMBER(12, 2) DEFAULT 0 NOT NULL,
	"FIXED_BONUS_IN_USD" NUMBER(12, 2) DEFAULT 0 NOT NULL
);

DROP SEQUENCE IF EXISTS "PUBLISHER_ACCOUNT_CAMPAIGN_REWARD_HISTORY_SEQUENCE";
CREATE SEQUENCE "PUBLISHER_ACCOUNT_CAMPAIGN_REWARD_HISTORY_SEQUENCE"
    MINVALUE 1
    MAXVALUE 9999999
    INCREMENT BY 1
    START WITH 1
    NOCACHE
    NOORDER
    CYCLE;

DROP TABLE IF EXISTS PUBLISHER_ACCOUNT_PAYMENT_HISTORY;
CREATE TABLE PUBLISHER_ACCOUNT_PAYMENT_HISTORY (
    "ID" NUMBER(10,0) NOT NULL,
    "PUBLISHER_ID" NUMBER(10,0) NOT NULL,
    "CAMPAIGN_CLOSURE_ID" NUMBER(10, 0) NOT NULL,
    "PAY_DATE" DATE,
    "REWARD_MONTH" DATE NOT NULL,
    "AMOUNT" NUMBER(15,2), 
    "PAYMENT_STATE" NUMBER(2,0), 
    "REQUEST_DATE" DATE, 
    "CREATED_BY" VARCHAR2(128), 
    "CREATED_ON" DATE, 
    "UPDATED_BY" VARCHAR2(128), 
    "UPDATED_ON" DATE, 
    "INVOICE_ID" VARCHAR2(17), 
    "AMOUNT_IN_USD" NUMBER(20,2)
);

DROP SEQUENCE IF EXISTS "PUBLISHER_ACCOUNT_PAYMENT_HISTORY_SEQUENCE";
CREATE SEQUENCE "PUBLISHER_ACCOUNT_PAYMENT_HISTORY_SEQUENCE"
    MINVALUE 1
    MAXVALUE 9999999
    INCREMENT BY 1
    START WITH 1
    NOCACHE
    NOORDER
    CYCLE;

DROP TABLE IF EXISTS PARTNER_ACCOUNT;
CREATE TABLE PARTNER_ACCOUNT (
    "ACCOUNT_NO" NUMBER(10,0) NOT NULL, 
    "ACCOUNT_TYPE_ID" NUMBER(1,0) DEFAULT 0, 
    "PARTNER_TYPE_ID" NUMBER(2,0), 
    "CORPORATE_NAME" VARCHAR2(128), 
    "SECTION_NAME" VARCHAR2(128), 
    "POST_NAME" VARCHAR2(128), 
    "LASTNAME" VARCHAR2(64), 
    "FIRSTNAME" VARCHAR2(64), 
    "EMAIL" VARCHAR2(64), 
    "ZIP_CODE" VARCHAR2(8), 
    "PREFECTURE" VARCHAR2(128), 
    "CITY" VARCHAR2(128), 
    "ADDRESS" VARCHAR2(512), 
    "ADDRESS2" VARCHAR2(512), 
    "PHONE" VARCHAR2(32),
    "BIRTHDAY" DATE, 
    "SEX" NUMBER(1,0),
    "COMMERCIAL_REGISTRATION_NUMBER" VARCHAR2(128), 
    "VAT_NUMBER" VARCHAR2(128), 
    "BANK_ID" VARCHAR2(5), 
    "BANK_NAME" VARCHAR2(128), 
    "BANK_BRANCH_ID" VARCHAR2(8), 
    "BANK_BRANCH_NAME" VARCHAR2(128), 
    "BANK_ACCOUNT_TYPE_ID" NUMBER(2,0), 
    "BANK_ACCOUNT_NUMBER" VARCHAR2(30), 
    "BANK_ACCOUNT_OWNER_LASTNAME" VARCHAR2(128), 
    "BANK_ACCOUNT_OWNER_FIRSTNAME" VARCHAR2(128),
    "URL" VARCHAR2(256), 
    "LOGIN_NAME" VARCHAR2(64), 
    "LOGIN_PASSWORD" VARCHAR2(32), 
    "ACCOUNT_STATE" NUMBER(1,0) NOT NULL, 
    "APPLIED_DATE" DATE NOT NULL, 
    "QUIT_DATE" DATE,
    "ORIGIN_NO" NUMBER(10,0), 
    "CREATED_BY" VARCHAR2(256), 
    "CREATED_ON" DATE, 
    "UPDATED_BY" VARCHAR2(256), 
    "UPDATED_ON" DATE, 
    "BLACKLIST_FLAG" NUMBER(1,0),
    "U_ID" VARCHAR2(32) DEFAULT 'v5542527tvx7w4ts6suvswssss2137xx',
    "COUNTRY_CODE" VARCHAR2(2) NOT NULL
); 

DROP TABLE IF EXISTS PARTNER_SITE;
CREATE TABLE PARTNER_SITE (
    "SITE_NO" NUMBER(10,0), 
    "ACCOUNT_NO" NUMBER(10,0), 
    "SITE_NAME" VARCHAR2(1024) DEFAULT 0, 
    "URL" VARCHAR2(1024) DEFAULT 0, 
    "DESCRIPTION" VARCHAR2(2000) DEFAULT 0, 
    "SITE_TYPE" NUMBER(2,0) DEFAULT 0, 
    "SITE_STATE" NUMBER(1,0) DEFAULT 0, 
    "CATEGORY_LOW_ID1" NUMBER(10,0), 
    "CATEGORY_LOW_ID2" NUMBER(10,0), 
    "CATEGORY_LOW_ID3" NUMBER(10,0),
    "MAIN_SITE_FLAG" NUMBER(1,0),
    "POINTBACK_FLAG" NUMBER(1,0), 
    "ALL_BANNERS_FLG" NUMBER(1,0) DEFAULT 0, 
    "CREATED_BY" VARCHAR2(256), 
    "CREATED_ON" DATE, 
    "UPDATED_BY" VARCHAR2(256), 
    "UPDATED_ON" DATE
);

DROP TABLE IF EXISTS SALES_LOG;
CREATE TABLE SALES_LOG (
    "SEQ_NO" NUMBER(10,0) NOT NULL, 
    "BANNER_ID" NUMBER(10,0) NOT NULL, 
    "MERCHANT_CAMPAIGN_NO" NUMBER(10,0) NOT NULL,
    "CLICK_DATE" DATE, 
    "SALES_DATE" DATE NOT NULL, 
    "LOG_DATE" DATE NOT NULL, 
    "CONFIRMED_DATE" DATE, 
    "TRANSACTION_ID" VARCHAR2(256 BYTE) NOT NULL, 
    "INTERNAL_TRANSACTION_ID" VARCHAR2(512 BYTE), 
    "PARTNER_SITE_NO" NUMBER(10,0) NOT NULL, 
    "RANK" NUMBER(2,0), 
    "VERIFY" VARCHAR2(256 BYTE), 
    "RESULT_ID" NUMBER(4,0), 
    "GOODS_ID" VARCHAR2(250 BYTE), 
    "SALES_LOG_STATUS" NUMBER(2,0) NOT NULL, 
    "SALES_COUNT" NUMBER(10,0) NOT NULL, 
    "PRICE" NUMBER(10,0) NOT NULL, 
    "TOTAL_PRICE" NUMBER(10,0) NOT NULL, 
    "REWARD_TYPE" NUMBER(1,0) NOT NULL, 
    "SALES_REWARD" NUMBER(12,2) NOT NULL, 
    "PUBLISHER_REWARD_IN_USD" NUMBER(12,2), 
    "TOTAL_PRICE_REWARD" NUMBER(12,2) NOT NULL, 
    "COMMISSION_TYPE" NUMBER(1,0) NOT NULL, 
    "AT_COMMISSION" NUMBER(12,2) NOT NULL, 
    "AGENT_COMMISSION" NUMBER(12,2) NOT NULL, 
    "P_AGENT_COMMISSION" NUMBER(12,2) DEFAULT 0 NOT NULL, 
    "IP" VARCHAR2(256 BYTE), 
    "MEDIA_URL" VARCHAR2(512 BYTE), 
    "REFERER" VARCHAR2(2048 BYTE), 
    "REPEAT_COUNT" NUMBER(10,0), 
    "USER_AGENT" VARCHAR2(512 BYTE), 
    "REWARD_EDIT_DATE" DATE,
    "DEFAULT_SALES_COUNT" NUMBER(10,0) NOT NULL, 
    "DEFAULT_PRICE" NUMBER(10,0) NOT NULL, 
    "DEFAULT_RESULT_ID" NUMBER(4,0), 
    "LP_URL" VARCHAR2(512 BYTE), 
    "DEVICE_TYPE" NUMBER(2,0), 
    "POINTBACK_ID" VARCHAR2(64 BYTE), 
    "PB_ID_DUPLICATIVE_FLAG" NUMBER(1,0), 
    "PB_ID_OLDEST_SALES_DATE" DATE, 
    "CREATED_BY" VARCHAR2(256 BYTE), 
    "CREATED_ON" DATE, 
    "UPDATED_BY" VARCHAR2(256 BYTE), 
    "UPDATED_ON" DATE, 
    "NEW_FLAG" NUMBER(1,0) DEFAULT 0, 
    "SESSION_ID" VARCHAR2(256 BYTE), 
    "CURRENCY_ID" NUMBER(4,0), 
    "UUID" VARCHAR2(64 BYTE) DEFAULT NULL, 
    "DEVICE_OS" NUMBER(2,0) DEFAULT 0 NOT NULL, 
    "CATEGORY_ID" VARCHAR2(250 BYTE), 
    "DISCOUNT" NUMBER(12,2) DEFAULT 0,
	"POSTBACK_STATUS" NUMBER(1,0) DEFAULT 1,
	"PUBLISHER_BONUS" NUMBER(12, 2) DEFAULT 0 NOT NULL,
	"PUBLISHER_BONUS_IN_USD" NUMBER(12, 2) DEFAULT 0 NOT NULL
);

DROP TABLE IF EXISTS CURRENCY_EXCHANGE_RATE_HISTORY;
CREATE TABLE CURRENCY_EXCHANGE_RATE_HISTORY (
    "CURRENCY" VARCHAR2(3) NOT NULL,
    "QUOTE_CURRENCY" VARCHAR2(3) NOT NULL,
    "TARGET_MONTH" DATE NOT NULL, 
    "RATE" NUMBER(20,10) NOT NULL, 
    "CREATED_BY" VARCHAR2(128), 
    "CREATED_ON" DATE, 
    "UPDATED_BY" VARCHAR2(128), 
    "UPDATED_ON" DATE
);

DROP TABLE IF EXISTS CURRENCY_MASTER;
CREATE TABLE CURRENCY_MASTER (
    "CURRENCY" VARCHAR2(3) NOT NULL, 
    "FRACTIONAL_DIGITS" NUMBER(1,0) DEFAULT 0 NOT NULL
);

DROP TABLE IF EXISTS MERCHANT_CAMPAIGN;
CREATE TABLE MERCHANT_CAMPAIGN (
    "CAMPAIGN_NO" NUMBER(10,0) NOT NULL, 
    "ACCOUNT_NO" NUMBER(10,0) NOT NULL
);

DROP TABLE IF EXISTS MERCHANT_ACCOUNT;
CREATE TABLE MERCHANT_ACCOUNT ( 
    "ACCOUNT_NO" NUMBER(10,0) NOT NULL,
    "COUNTRY_CODE" VARCHAR2(2) NOT NULL
);

DROP TABLE IF EXISTS MONTHLY_CLOSING;
CREATE TABLE MONTHLY_CLOSING (
    "CLOSED_MONTH" CHAR(6 BYTE) NOT NULL, 
    "TARGET_MONTH" CHAR(6 BYTE) NOT NULL, 
    "TEMPORARY_CLOSING_FLAG" NUMBER(1,0) NOT NULL, 
    "CREATED_BY" VARCHAR2(256 BYTE), 
    "CREATED_ON" DATE, 
    "UPDATED_BY" VARCHAR2(256 BYTE), 
    "UPDATED_ON" DATE,
    "COUNTRY_CODE" VARCHAR2(2) NOT NULL
);

DROP TABLE IF EXISTS BONUS;
CREATE TABLE BONUS (
	"ID" NUMBER(10, 0) NOT NULL,
	"SITE_ID" NUMBER(10, 0) NOT NULL,
	"CAMPAIGN_ID" NUMBER(10, 0) NOT NULL,
	"PUBLISHER_BONUS" NUMBER(12 ,2) DEFAULT 0 NOT NULL,
	"PUBLISHER_BONUS_IN_USD" NUMBER(12 ,2) DEFAULT 0 NOT NULL,
	"STATUS" NUMBER(2 ,0) NOT NULL,
	"CONFIRMED_DATE" DATE
);