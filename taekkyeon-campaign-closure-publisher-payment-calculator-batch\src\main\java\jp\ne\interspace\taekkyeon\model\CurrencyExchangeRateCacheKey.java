/**
 * Copyright © 2021 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.model;

import java.time.YearMonth;

import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;

/**
 * DTO for holding data for finding currency exchange rate.
 *
 * <AUTHOR>
 */
@AllArgsConstructor @Getter @EqualsAndHashCode
public class CurrencyExchangeRateCacheKey {

    private final String baseCurrency;
    private final String quoteCurrency;
    private final YearMonth targetMonth;
}
